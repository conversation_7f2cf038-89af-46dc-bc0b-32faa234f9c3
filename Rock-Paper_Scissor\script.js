// Game State Management
class GameClient {
    constructor() {
        this.socket = null;
        this.playerId = null;
        this.playerName = '';
        this.roomCode = '';
        this.currentScreen = 'welcome-screen';
        this.gameState = 'waiting';
        this.players = [];
        this.roundTimer = null;
        this.resultTimer = null;
        this.timeLeft = 30;
        this.botMode = false;
        this.botGame = null;

        this.init();
    }
    
    init() {
        this.connectSocket();
        this.setupEventListeners();
        this.showScreen('welcome-screen');
    }

    getDefaultServerUrl() {
        // Fallback configuration if config.js is not loaded
        const isLocal = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname === '';

        return isLocal
            ? 'http://localhost:5000'
            : 'https://your-app-name.onrender.com'; // Replace with your actual URL
    }

    connectSocket() {
        // Get server URL from configuration
        const serverUrl = typeof CONFIG !== 'undefined'
            ? CONFIG.getServerUrl()
            : this.getDefaultServerUrl();

        this.socket = io(serverUrl, {
            transports: ['websocket', 'polling'],
            timeout: 20000,
            forceNew: true
        });
        
        this.socket.on('connect', () => {
            this.updateConnectionStatus(true);
            this.showToast('Connected to server!');
            console.log('Connected to server successfully');
        });
        
        this.socket.on('disconnect', () => {
            this.updateConnectionStatus(false);
            this.showToast('Disconnected from server', 'error');
            console.log('Disconnected from server');
        });
        
        this.socket.on('connect_error', (error) => {
            this.updateConnectionStatus(false);
            this.showToast('Connection failed. Please try again.', 'error');
            console.error('Connection error:', error);
        });
        
        this.socket.on('connected', (data) => {
            this.playerId = data.player_id;
            console.log('Player ID assigned:', this.playerId);
        });
        
        this.socket.on('room_created', (data) => {
            this.roomCode = data.room_code;
            this.playerName = data.player_name;
            this.showScreen('waiting-screen');
            this.updateRoomDisplay();
            this.showToast(`Room ${this.roomCode} created!`);
        });
        
        this.socket.on('room_joined', (data) => {
            this.roomCode = data.room_code;
            this.playerName = data.player_name;
            this.players = data.players;
            this.showScreen('waiting-screen');
            this.updateRoomDisplay();
            this.updatePlayersList();
            this.showToast(`Joined room ${this.roomCode}!`);
        });
        
        this.socket.on('join_error', (data) => {
            this.showError('join-error', data.message);
        });
        
        this.socket.on('player_joined', (data) => {
            this.players = data.players;
            this.updatePlayersList();
            
            if (data.game_ready) {
                this.showScreen('game-screen');
                this.updateGameHeader();
                // Initialize round info for new game
                const initialScores = {};
                data.players.forEach(player => {
                    initialScores[player.id] = 0;
                });
                this.updateRoundInfo(1, initialScores);
                this.showToast('Game is starting!');
            } else {
                this.showToast(`${data.player_name} joined the room!`);
            }
        });
        
        this.socket.on('player_left', (data) => {
            this.showToast(data.message, 'error');
            this.showScreen('waiting-screen');
            this.updateWaitingMessage();
        });
        
        this.socket.on('choice_recorded', (data) => {
            this.showChoiceFeedback('Choice recorded! Waiting for opponent...', 'recorded');
            this.disableChoiceButtons();
        });
        
        this.socket.on('player_chose', (data) => {
            if (data.player_id !== this.playerId) {
                this.showChoiceFeedback('Opponent has chosen! Waiting for you...', 'waiting');
            }
        });
        
        this.socket.on('game_result', (data) => {
            this.showResults(data);
        });
        
        this.socket.on('new_round', (data) => {
            this.showScreen('game-screen');
            this.resetGameState();
            this.updateRoundInfo(data.current_round, data.scores);
            this.showToast(data.message);
        });
    }
    
    setupEventListeners() {
        // Welcome screen
        document.getElementById('create-room-btn').addEventListener('click', () => {
            this.createRoom();
        });
        
        document.getElementById('join-room-btn').addEventListener('click', () => {
            this.showScreen('join-screen');
        });

        document.getElementById('play-bot-btn').addEventListener('click', () => {
            this.startBotGame();
        });
        
        // Join screen
        document.getElementById('join-confirm-btn').addEventListener('click', () => {
            this.joinRoom();
        });
        
        document.getElementById('back-to-welcome-btn').addEventListener('click', () => {
            this.showScreen('welcome-screen');
        });
        
        // Room code input formatting
        document.getElementById('room-code').addEventListener('input', (e) => {
            e.target.value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        });
        
        // Enter key handling
        document.getElementById('player-name').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('create-room-btn').click();
            }
        });
        
        document.getElementById('room-code').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('join-confirm-btn').click();
            }
        });
        
        // Waiting screen
        document.getElementById('copy-code-btn').addEventListener('click', () => {
            this.copyRoomCode();
        });
        
        document.getElementById('leave-room-btn').addEventListener('click', () => {
            this.leaveRoom();
        });
        
        // Game screen
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.makeChoice(e.currentTarget.dataset.choice);
            });
        });
        
        // Results screen
        document.getElementById('play-again-btn').addEventListener('click', () => {
            this.playAgain();
        });
        
        document.getElementById('leave-game-btn').addEventListener('click', () => {
            this.leaveRoom();
        });
    }
    
    createRoom() {
        const playerName = document.getElementById('player-name').value.trim();
        if (!playerName) {
            this.showToast('Please enter your name', 'error');
            return;
        }
        
        if (!this.socket || !this.socket.connected) {
            this.showToast('Not connected to server. Please wait...', 'error');
            return;
        }
        
        this.socket.emit('create_room', { player_name: playerName });
    }
    
    joinRoom() {
        const playerName = document.getElementById('player-name').value.trim();
        const roomCode = document.getElementById('room-code').value.trim();
        
        if (!playerName) {
            this.showToast('Please enter your name', 'error');
            return;
        }
        
        if (!roomCode || roomCode.length !== 6) {
            this.showError('join-error', 'Please enter a valid 6-digit room code');
            return;
        }
        
        if (!this.socket || !this.socket.connected) {
            this.showToast('Not connected to server. Please wait...', 'error');
            return;
        }
        
        this.hideError('join-error');
        this.socket.emit('join_room', { 
            player_name: playerName, 
            room_code: roomCode 
        });
    }
    
    makeChoice(choice) {
        if (this.botMode) {
            // Handle bot game
            this.clearTimers();
            const result = this.botGame.makeChoice(choice);

            // Visual feedback
            document.querySelectorAll('.choice-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector(`[data-choice="${choice}"]`).classList.add('selected');

            // Show results after a short delay
            setTimeout(() => {
                this.showResults(result);
            }, 1000);
            return;
        }

        if (!this.socket || !this.socket.connected) {
            this.showToast('Not connected to server', 'error');
            return;
        }

        this.socket.emit('make_choice', { choice: choice });

        // Visual feedback
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        document.querySelector(`[data-choice="${choice}"]`).classList.add('selected');
    }
    
    playAgain() {
        if (this.botMode) {
            // Handle bot game restart - start completely new game
            this.botGame = new BotGame(this.playerName);
            this.showScreen('game-screen');
            this.resetGameState();
            this.updateRoundInfo(1, { 'player': 0, 'bot': 0 });
            return;
        }

        if (!this.socket || !this.socket.connected) {
            this.showToast('Not connected to server', 'error');
            return;
        }

        this.socket.emit('play_again', {});
    }
    
    leaveRoom() {
        if (this.botMode) {
            // Reset bot mode
            this.botMode = false;
            this.botGame = null;
            document.getElementById('connection-status').style.display = 'block';
            this.showScreen('welcome-screen');
            this.resetGameState();
            this.showToast('Left bot game');
            return;
        }

        if (this.socket) {
            this.socket.disconnect();
            this.socket.connect();
        }
        this.showScreen('welcome-screen');
        this.resetGameState();
        this.showToast('Left the room');
    }
    
    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
        this.currentScreen = screenId;
    }
    
    updateConnectionStatus(connected) {
        const indicator = document.getElementById('connection-indicator');
        const text = document.getElementById('connection-text');
        
        if (connected) {
            indicator.textContent = '🟢';
            text.textContent = 'Connected';
        } else {
            indicator.textContent = '🔴';
            text.textContent = 'Disconnected';
        }
    }
    
    updateRoomDisplay() {
        document.getElementById('display-room-code').textContent = this.roomCode;
        document.getElementById('game-room-code').textContent = this.roomCode;
    }
    
    updatePlayersList() {
        const container = document.getElementById('players-container');
        container.innerHTML = '';
        
        this.players.forEach(player => {
            const playerDiv = document.createElement('div');
            playerDiv.className = 'player-item';
            playerDiv.innerHTML = `
                <span>${player.name} ${player.id === this.playerId ? '(You)' : ''}</span>
                <span class="player-status">${player.connected ? '🟢 Online' : '🔴 Offline'}</span>
            `;
            container.appendChild(playerDiv);
        });
        
        this.updateWaitingMessage();
    }
    
    updateWaitingMessage() {
        const message = document.getElementById('waiting-message');
        if (this.players.length < 2) {
            message.textContent = 'Waiting for another player to join...';
            message.classList.add('pulse');
        } else {
            message.textContent = 'Game is ready to start!';
            message.classList.remove('pulse');
        }
    }
    
    updateGameHeader(scores = null) {
        const playersInfo = document.getElementById('game-players');

        if (this.botMode) {
            // Bot mode display
            const playerScore = scores ? (scores['player'] || 0) : 0;
            const botScore = scores ? (scores['bot'] || 0) : 0;
            playersInfo.innerHTML = `
                <span>${this.playerName} (You): ${playerScore}</span>
                <span class="vs-text"> vs </span>
                <span>Bot: ${botScore}</span>
            `;
            return;
        }

        if (scores && this.players.length === 2) {
            // Show players with scores
            const player1 = this.players[0];
            const player2 = this.players[1];
            const score1 = scores[player1.id] || 0;
            const score2 = scores[player2.id] || 0;

            playersInfo.innerHTML = `
                <span>${player1.name}${player1.id === this.playerId ? ' (You)' : ''}: ${score1}</span>
                <span class="vs-text"> vs </span>
                <span>${player2.name}${player2.id === this.playerId ? ' (You)' : ''}: ${score2}</span>
            `;
        } else {
            // Show players without scores (initial state)
            playersInfo.innerHTML = this.players.map(p =>
                `<span>${p.name}${p.id === this.playerId ? ' (You)' : ''}</span>`
            ).join(' vs ');
        }
    }
    
    showChoiceFeedback(message, type) {
        const feedback = document.getElementById('choice-feedback');
        feedback.textContent = message;
        feedback.className = `choice-feedback show ${type}`;
    }
    
    disableChoiceButtons() {
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.disabled = true;
        });
    }
    
    resetGameState() {
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('selected');
        });

        document.getElementById('choice-feedback').classList.remove('show');
        // Don't reset game-status here as it will be updated by updateRoundInfo
    }
    
    showResults(data) {
        this.clearTimers();
        this.showScreen('results-screen');

        // Hide/show buttons based on game completion
        const playAgainBtn = document.getElementById('play-again-btn');
        if (data.game_complete) {
            // Final game - show "Start New Game" button
            playAgainBtn.style.display = 'block';
            playAgainBtn.innerHTML = '<span class="btn-icon">🎯</span>Start New Game';
        } else {
            // Round result - hide the button (auto-advance will handle)
            playAgainBtn.style.display = 'none';
        }

        const choiceEmojis = {
            rock: '✊',
            paper: '✋',
            scissors: '✌️'
        };
        
        // Update player choices display
        const players = Object.keys(data.players);
        const player1Id = players[0];
        const player2Id = players[1];
        
        document.getElementById('player1-name').textContent = data.players[player1Id];
        document.getElementById('player2-name').textContent = data.players[player2Id];
        document.getElementById('player1-choice').textContent = choiceEmojis[data.choices[player1Id]];
        document.getElementById('player2-choice').textContent = choiceEmojis[data.choices[player2Id]];
        
        // Update result message
        const resultMessage = document.getElementById('result-message');
        const resultTitle = document.getElementById('result-title');

        // Update round info and scores
        this.updateRoundInfo(data.current_round, data.scores);

        if (data.game_complete) {
            // Show final game result with enhanced champion screen
            const playerIds = Object.keys(data.scores);
            const myId = this.botMode ? 'player' : this.playerId;
            const myScore = data.scores[myId] || 0;
            const opponentId = this.botMode ? 'bot' : playerIds.find(id => id !== this.playerId);
            const opponentScore = data.scores[opponentId] || 0;
            const opponentName = data.players[opponentId] || 'Opponent';

            if (data.game_winner_id === myId) {
                const myName = data.players[myId] || 'You';
                resultTitle.innerHTML = "🎉 CHAMPION! 🎉";
                resultMessage.innerHTML = `
                    <div class="champion-message">
                        <div class="trophy">🏆</div>
                        <div class="victory-text">VICTORY!</div>
                        <div class="player-scores">
                            <div class="winner-score">${myName}: ${myScore}</div>
                            <div class="loser-score">${opponentName}: ${opponentScore}</div>
                        </div>
                        <div class="victory-details">You defeated ${opponentName} in an epic best-of-3 battle!</div>
                        <div class="celebration">Congratulations!</div>
                    </div>
                `;
                resultMessage.className = 'result-message win champion';
            } else if (data.game_winner_id && data.game_winner_id !== myId) {
                const myName = data.players[myId] || 'You';
                resultTitle.textContent = "Game Over";
                resultMessage.innerHTML = `
                    <div class="defeat-message">
                        <div class="defeat-text">Defeat</div>
                        <div class="player-scores">
                            <div class="winner-score">${opponentName}: ${opponentScore}</div>
                            <div class="loser-score">${myName}: ${myScore}</div>
                        </div>
                        <div class="defeat-details">${opponentName} won this best-of-3 battle!</div>
                        <div class="encouragement">Better luck next time! 💪</div>
                    </div>
                `;
                resultMessage.className = 'result-message lose';
            } else {
                // Handle tie case (shouldn't happen often with new logic)
                resultTitle.textContent = "It's a Tie!";
                resultMessage.innerHTML = `
                    <div class="tie-message">
                        <div class="tie-text">Draw!</div>
                        <div class="final-score">Final Score: ${myScore} - ${opponentScore}</div>
                        <div class="tie-details">What an evenly matched battle!</div>
                    </div>
                `;
                resultMessage.className = 'result-message tie';
            }
        } else {
            // Show round result
            const myId = this.botMode ? 'player' : this.playerId;
            if (data.round_result === 'tie') {
                resultTitle.textContent = `Round ${data.current_round} - Tie!`;
                resultMessage.textContent = "Great minds think alike! No points awarded.";
                resultMessage.className = 'result-message tie';
            } else if (data.round_winner_id === myId) {
                resultTitle.textContent = `Round ${data.current_round} - You Win! 🎉`;
                resultMessage.textContent = "You earned a point this round!";
                resultMessage.className = 'result-message win';
            } else {
                resultTitle.textContent = `Round ${data.current_round} - You Lose 😔`;
                resultMessage.textContent = "Your opponent earned a point this round.";
                resultMessage.className = 'result-message lose';
            }

            // Auto-advance to next round after 5 seconds (only for non-final rounds)
            if (!data.game_complete) {
                this.resultTimer = setTimeout(() => {
                    this.playAgain();
                }, 5000);
            }
        }
    }

    updateRoundInfo(currentRound, scores) {
        // Update round info in game screen
        const gameStatus = document.getElementById('game-status');
        if (gameStatus && scores) {
            let myScore, opponentScore;

            if (this.botMode) {
                // Bot mode - use 'player' and 'bot' keys
                myScore = scores['player'] || 0;
                opponentScore = scores['bot'] || 0;
            } else {
                // Multiplayer mode - use player IDs
                const playerIds = Object.keys(scores);
                myScore = scores[this.playerId] || 0;
                opponentScore = playerIds.length > 1 ? scores[playerIds.find(id => id !== this.playerId)] || 0 : 0;
            }

            // Update game status to show round and score
            const roundDisplay = currentRound <= 3 ? `Round ${currentRound} of 3` : `Round ${currentRound} (Overtime)`;
            gameStatus.innerHTML = `
                <div>${roundDisplay}</div>
                <div>Score: ${myScore} - ${opponentScore}</div>
                <div>Make your choice!</div>
                <div id="timer-display" class="timer-display">Time: 30s</div>
            `;

            // Start round timer
            this.startRoundTimer();

            // Also update the game header with scores
            this.updateGameHeader(scores);
        }
    }

    startRoundTimer() {
        this.clearTimers();
        this.timeLeft = 30;
        this.updateTimerDisplay();

        this.roundTimer = setInterval(() => {
            this.timeLeft--;
            this.updateTimerDisplay();

            if (this.timeLeft <= 0) {
                this.clearTimers();
                // Auto-select rock if no choice made
                if (this.currentScreen === 'game-screen') {
                    this.makeChoice('rock');
                }
            }
        }, 1000);
    }

    updateTimerDisplay() {
        const timerDisplay = document.getElementById('timer-display');
        if (timerDisplay) {
            timerDisplay.textContent = `Time: ${this.timeLeft}s`;
            if (this.timeLeft <= 10) {
                timerDisplay.classList.add('timer-warning');
            } else {
                timerDisplay.classList.remove('timer-warning');
            }
        }
    }

    clearTimers() {
        if (this.roundTimer) {
            clearInterval(this.roundTimer);
            this.roundTimer = null;
        }
        if (this.resultTimer) {
            clearTimeout(this.resultTimer);
            this.resultTimer = null;
        }
    }

    copyRoomCode() {
        navigator.clipboard.writeText(this.roomCode).then(() => {
            this.showToast('Room code copied to clipboard!');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = this.roomCode;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                this.showToast('Room code copied to clipboard!');
            } catch (err) {
                this.showToast('Failed to copy room code', 'error');
            }
            document.body.removeChild(textArea);
        });
    }
    
    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
    
    showError(elementId, message) {
        const errorElement = document.getElementById(elementId);
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }
    
    hideError(elementId) {
        const errorElement = document.getElementById(elementId);
        errorElement.classList.remove('show');
    }

    // Bot Game Methods
    startBotGame() {
        const playerName = document.getElementById('player-name').value.trim();
        if (!playerName) {
            this.showToast('Please enter your name', 'error');
            return;
        }

        this.botMode = true;
        this.playerName = playerName;
        this.botGame = new BotGame(playerName);
        this.showScreen('game-screen');

        // Hide connection status for bot mode
        document.getElementById('connection-status').style.display = 'none';

        // Initialize bot game display
        this.updateGameHeader();
        this.updateRoundInfo(1, { 'player': 0, 'bot': 0 });
        this.showToast('Bot game started!');
    }
}

// Bot Game Class
class BotGame {
    constructor(playerName) {
        this.playerName = playerName;
        this.botName = 'Bot';
        this.currentRound = 1;
        this.maxRounds = 3;
        this.playerScore = 0;
        this.botScore = 0;
        this.choices = ['rock', 'paper', 'scissors'];
    }

    makeChoice(playerChoice) {
        const botChoice = this.choices[Math.floor(Math.random() * 3)];
        const result = this.determineWinner(playerChoice, botChoice);

        // Store current round number before potentially incrementing
        const currentRoundNumber = this.currentRound;

        if (result === 'player') {
            this.playerScore++;
        } else if (result === 'bot') {
            this.botScore++;
        }

        const gameComplete = this.isGameComplete();
        const gameWinner = gameComplete ? this.determineGameWinner() : null;

        const resultData = {
            round_result: result === 'tie' ? 'tie' : 'win',
            round_winner_id: result === 'player' ? 'player' : (result === 'bot' ? 'bot' : null),
            game_complete: gameComplete,
            game_winner_id: gameWinner,
            current_round: currentRoundNumber,
            total_rounds: this.maxRounds,
            scores: { 'player': this.playerScore, 'bot': this.botScore },
            choices: { 'player': playerChoice, 'bot': botChoice },
            players: { 'player': this.playerName, 'bot': this.botName }
        };

        // Increment round number after creating result data (for next round)
        if (!gameComplete) {
            this.currentRound++;
        }

        return resultData;
    }

    determineWinner(playerChoice, botChoice) {
        if (playerChoice === botChoice) return 'tie';

        const winConditions = {
            rock: 'scissors',
            paper: 'rock',
            scissors: 'paper'
        };

        return winConditions[playerChoice] === botChoice ? 'player' : 'bot';
    }

    isGameComplete() {
        // Game ends when someone reaches 2 wins (majority of 3)
        if (this.playerScore >= 2 || this.botScore >= 2) {
            return true;
        }

        // If we've played 3 rounds and no one has 2 wins, continue until someone gets 2
        if (this.currentRound >= this.maxRounds) {
            if (this.playerScore === this.botScore) {
                return false; // Continue playing
            }
            return true;
        }

        return false;
    }

    determineGameWinner() {
        if (this.playerScore > this.botScore) {
            return 'player';
        } else if (this.botScore > this.playerScore) {
            return 'bot';
        }
        return null; // Tie
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new GameClient();
});
