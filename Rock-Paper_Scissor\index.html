<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GamyDay Games - Rock Paper Scissors</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        /* Loading Screen Styles (from <PERSON><PERSON>) */
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .logo-float {
            animation: float 4s ease-in-out infinite;
        }

        @keyframes shine {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .progress-bar-shine::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 60%);
            background-size: 200% 100%;
            animation: shine 2s infinite linear;
            z-index: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 1s ease-out forwards;
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        .fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }

        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        #gameUI {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.5s ease-in, visibility 0.5s;
        }

        #gameUI.active {
            visibility: visible;
            opacity: 1;
        }

        .glow-bar {
            box-shadow: 0 0 12px rgba(255, 186, 60, 0.5), 0 0 20px rgba(255, 115, 40, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🎮 Rock Paper Scissors Online</h1>
            <p>Challenge your friends in real-time!</p>
        </header>

        <!-- Welcome Screen -->
        <div id="welcome-screen" class="screen active">
            <div class="welcome-content">
                <div class="input-group">
                    <label for="player-name">Enter your name:</label>
                    <input type="text" id="player-name" placeholder="Your name" maxlength="20">
                </div>
                
                <div class="button-group">
                    <button id="create-room-btn" class="btn btn-primary">
                        <span class="btn-icon">🎯</span>
                        Create New Game
                    </button>
                    <button id="join-room-btn" class="btn btn-secondary">
                        <span class="btn-icon">🚪</span>
                        Join Game
                    </button>
                    <button id="play-bot-btn" class="btn btn-danger">
                        <span class="btn-icon">🤖</span>
                        Play with Bot
                    </button>
                </div>
            </div>
        </div>

        <!-- Join Room Screen -->
        <div id="join-screen" class="screen">
            <div class="join-content">
                <h2>Join a Game</h2>
                <div class="input-group">
                    <label for="room-code">Enter 6-digit room code:</label>
                    <input type="text" id="room-code" placeholder="ABC123" maxlength="6" style="text-transform: uppercase;">
                </div>
                <div class="button-group">
                    <button id="join-confirm-btn" class="btn btn-primary">Join Game</button>
                    <button id="back-to-welcome-btn" class="btn btn-secondary">Back</button>
                </div>
                <div id="join-error" class="error-message"></div>
            </div>
        </div>

        <!-- Waiting Room Screen -->
        <div id="waiting-screen" class="screen">
            <div class="waiting-content">
                <h2>Game Room</h2>
                <div class="room-info">
                    <div class="room-code-display">
                        <label>Room Code:</label>
                        <div class="room-code" id="display-room-code">------</div>
                        <button id="copy-code-btn" class="btn btn-small">📋 Copy</button>
                    </div>
                </div>
                
                <div class="players-list">
                    <h3>Players</h3>
                    <div id="players-container">
                        <!-- Players will be dynamically added here -->
                    </div>
                </div>
                
                <div id="waiting-message" class="status-message">
                    Waiting for another player to join...
                </div>
                
                <button id="leave-room-btn" class="btn btn-danger">Leave Room</button>
            </div>
        </div>

        <!-- Game Screen -->
        <div id="game-screen" class="screen">
            <div class="game-content">
                <div class="game-header">
                    <div class="room-code-small">Room: <span id="game-room-code">------</span></div>
                    <div class="players-info" id="game-players">
                        <!-- Player info will be shown here -->
                    </div>
                </div>
                
                <div class="game-status" id="game-status">
                    <div>Round 1 of 10</div>
                    <div>Score: 0 - 0</div>
                    <div>Make your choice!</div>
                </div>
                
                <div class="choices-container">
                    <button class="choice-btn" data-choice="rock">
                        <span class="choice-emoji">✊</span>
                        <span class="choice-name">Rock</span>
                    </button>
                    <button class="choice-btn" data-choice="paper">
                        <span class="choice-emoji">✋</span>
                        <span class="choice-name">Paper</span>
                    </button>
                    <button class="choice-btn" data-choice="scissors">
                        <span class="choice-emoji">✌️</span>
                        <span class="choice-name">Scissors</span>
                    </button>
                </div>
                
                <div id="choice-feedback" class="choice-feedback"></div>
            </div>
        </div>

        <!-- Results Screen -->
        <div id="results-screen" class="screen">
            <div class="results-content">
                <div class="result-header">
                    <h2 id="result-title">Game Result</h2>
                </div>
                
                <div class="choices-display">
                    <div class="player-choice">
                        <div class="player-name" id="player1-name">Player 1</div>
                        <div class="choice-display" id="player1-choice">✊</div>
                    </div>

                    <div class="vs-divider">VS</div>

                    <div class="player-choice">
                        <div class="player-name" id="player2-name">Player 2</div>
                        <div class="choice-display" id="player2-choice">✋</div>
                    </div>
                </div>
                
                <div class="result-message" id="result-message">
                    <!-- Result message will be shown here -->
                </div>
                
                <div class="button-group">
                    <button id="play-again-btn" class="btn btn-primary">
                        <span class="btn-icon">🔄</span>
                        Play Again
                    </button>
                    <button id="leave-game-btn" class="btn btn-secondary">
                        <span class="btn-icon">🚪</span>
                        Leave Game
                    </button>
                </div>
            </div>
        </div>

        <!-- Connection Status -->
        <div id="connection-status" class="connection-status">
            <span id="connection-indicator">🔴</span>
            <span id="connection-text">Connecting...</span>
        </div>
    </div>

    <!-- Credits Footer -->
    <footer class="credits-footer">
        <div class="credits-content">
            <div class="credits-title">Created by Shushie</div>
            <div class="credits-links">
                <a href="https://www.youtube.com/@Shushie_valorant?sub_confirmation=1" target="_blank" rel="noopener noreferrer" class="credits-link youtube-link">
                    <span class="link-icon">📺</span>
                    Subscribe on YouTube
                </a>
                <a href="https://razorpay.me/@shushie" target="_blank" rel="noopener noreferrer" class="credits-link coffee-link">
                    <span class="link-icon">☕</span>
                    Buy Me a Coffee
                </a>
            </div>
        </div>
    </footer>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>
