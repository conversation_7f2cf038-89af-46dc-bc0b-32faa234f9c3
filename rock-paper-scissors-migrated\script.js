// Game state variables
let ws = null;
let gameStarted = false;
let gameOver = false;
let mySymbol = null;
let currentRound = 1;
let maxRounds = 3;
let scores = {};
let players = {};
let gameState = 'waiting';
let myChoice = null;
let opponentChoice = null;

// DOM elements
const loadingScreen = document.getElementById('loadingScreen');
const gameUI = document.getElementById('gameUI');
const progressBar = document.getElementById('progressBar');
const loadingMessage = document.getElementById('loadingMessage');
const player1Name = document.getElementById('player1Name');
const player2Name = document.getElementById('player2Name');
const gameStatus = document.getElementById('gameStatus');
const roundInfo = document.getElementById('roundInfo');
const scoreInfo = document.getElementById('scoreInfo');
const turnInfo = document.getElementById('turnInfo');
const choiceButtons = document.querySelectorAll('.choice-btn');
const resultsContainer = document.getElementById('resultsContainer');
const roundResult = document.getElementById('roundResult');
const player1Choice = document.getElementById('player1Choice');
const player2Choice = document.getElementById('player2Choice');
const player1ChoiceName = document.getElementById('player1ChoiceName');
const player2ChoiceName = document.getElementById('player2ChoiceName');
const gameOverMessage = document.getElementById('gameOverMessage');
const modal = document.getElementById('modal');
const modalTitle = document.getElementById('modalTitle');
const modalMessage = document.getElementById('modalMessage');
const modalButton = document.getElementById('modalButton');

// Choice emojis mapping
const choiceEmojis = {
    rock: '✊',
    paper: '✋',
    scissors: '✌️'
};

// Initialize the game
function init() {
    console.log('Initializing Rock Paper Scissors game...');
    setupEventListeners();
    startLoading();
}

function setupEventListeners() {
    // Choice buttons
    choiceButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            handleChoiceClick(e.currentTarget.dataset.choice);
        });
    });

    // Modal button
    modalButton.addEventListener('click', () => {
        hideModal();
    });
}

function startLoading() {
    let progress = 0;
    const messages = [
        'Initializing...',
        'Connecting to server...',
        'Loading game assets...',
        'Preparing game room...',
        'Almost ready...'
    ];
    let messageIndex = 0;

    const interval = setInterval(() => {
        progress += Math.random() * 8 + 2;

        // Update message based on progress
        const newMessageIndex = Math.floor((progress / 100) * messages.length);
        if (newMessageIndex !== messageIndex && newMessageIndex < messages.length) {
            messageIndex = newMessageIndex;
            loadingMessage.innerText = messages[messageIndex];
        }

        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
            loadingMessage.innerText = 'Ready!';
            setTimeout(() => {
                fetchWebSocketUrl();
            }, 500);
        }
        progressBar.style.width = progress + '%';
    }, 150);
}

async function fetchWebSocketUrl() {
    try {
        loadingMessage.innerText = 'Connecting to game server...';
        const response = await fetch('/game-play/room-service', {
            method: 'GET',
            credentials: 'include'
        });
        const data = await response.json();
        if (data.wsURL) {
            connectWebSocket(data.wsURL);
        } else {
            throw new Error('No WebSocket URL provided');
        }
    } catch (error) {
        console.error('Error fetching WebSocket URL:', error);
        loadingMessage.innerText = 'Error connecting to server...';
        setTimeout(() => {
            showErrorModal('Failed to connect to game server. Please try again.');
        }, 1000);
    }
}

function connectWebSocket(wsUrl) {
    ws = new WebSocket(wsUrl);

    ws.onopen = () => {
        console.log('WebSocket connected');
        loadingMessage.innerText = 'Connected! Waiting for players...';
    };

    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        console.log('Received message:', data);
        handleWebSocketMessage(data);
    };

    ws.onclose = () => {
        console.log('WebSocket closed');
        showErrorModal('Connection lost. Please try again.');
    };

    ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        showErrorModal('Connection error. Please try again.');
    };
}

function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'game_state':
            handleInitialGameState(data.state);
            break;
        case 'game_start':
            handleGameStart(data);
            break;
        case 'game_update':
            handleGameUpdate(data.state);
            break;
        case 'personal_state':
            handlePersonalState(data.state);
            break;
        case 'player_joined':
            handlePlayerJoined(data);
            break;
        case 'player_left':
            handlePlayerLeft(data);
            break;
        case 'game_over':
            handleGameOver(data);
            break;
        case 'round_result':
            handleRoundResult(data);
            break;
        case 'pong':
            break;
        case 'error':
            showErrorModal(data.message);
            break;
        default:
            console.warn('Unknown message type:', data.type);
    }
}

function handleGameStart(data) {
    console.log('Game starting:', data);
    gameStarted = true;
    gameOver = false;

    if (data.state) {
        handleGameUpdate(data.state);
    }

    if (loadingScreen.style.display !== 'none') {
        finishLoading();
    }
}

function handleInitialGameState(state) {
    console.log('Initial game state:', state);

    if (state.players) {
        players = state.players;
        updatePlayerNames();
    }
    if (state.currentRound !== undefined) {
        currentRound = state.currentRound;
    }
    if (state.maxRounds !== undefined) {
        maxRounds = state.maxRounds;
    }
    if (state.scores) {
        scores = state.scores;
    }
    if (state.isGameOver !== undefined) {
        gameOver = state.isGameOver;
    }

    updateGameStatus();
}

function handleGameUpdate(state) {
    console.log('Game update:', state);
    
    if (state.players) {
        players = state.players;
        updatePlayerNames();
    }
    if (state.currentRound !== undefined) {
        currentRound = state.currentRound;
    }
    if (state.scores) {
        scores = state.scores;
    }
    if (state.isGameOver !== undefined) {
        gameOver = state.isGameOver;
    }

    updateGameStatus();
    
    if (gameOver) {
        showGameOverResults(state);
    }
}

function handlePersonalState(state) {
    console.log('Personal state:', state);
    if (state.symbol) {
        mySymbol = state.symbol;
    }
}

function handlePlayerJoined(data) {
    console.log('Player joined:', data);
    if (data.players) {
        players = data.players;
        updatePlayerNames();
    }
}

function handlePlayerLeft(data) {
    console.log('Player left:', data);
    showErrorModal('Your opponent has left the game.');
}

function handleGameOver(data) {
    console.log('Game over:', data);
    gameOver = true;
    showGameOverResults(data.state);
}

function handleRoundResult(data) {
    console.log('Round result:', data);
    showRoundResults(data);
}

function finishLoading() {
    loadingScreen.classList.add('fade-out');
    setTimeout(() => {
        loadingScreen.style.display = 'none';
        gameUI.classList.add('active');
    }, 500);
}

function updatePlayerNames() {
    const playerIds = Object.keys(players);
    if (playerIds.length >= 1) {
        player1Name.textContent = players[playerIds[0]] || 'Player 1';
    }
    if (playerIds.length >= 2) {
        player2Name.textContent = players[playerIds[1]] || 'Player 2';
    }
}

function updateGameStatus() {
    roundInfo.textContent = `Round ${currentRound} of ${maxRounds}`;
    
    const playerIds = Object.keys(scores);
    if (playerIds.length >= 2) {
        scoreInfo.textContent = `Score: ${scores[playerIds[0]] || 0} - ${scores[playerIds[1]] || 0}`;
    } else {
        scoreInfo.textContent = 'Score: 0 - 0';
    }

    if (gameOver) {
        turnInfo.textContent = 'Game Over!';
        disableChoiceButtons();
    } else {
        turnInfo.textContent = 'Make your choice!';
        enableChoiceButtons();
    }
}

function handleChoiceClick(choice) {
    console.log(`Choice clicked: ${choice}`);

    if (gameOver) {
        console.log('Click ignored: Game is over');
        return;
    }

    if (myChoice) {
        console.log('Click ignored: Choice already made');
        return;
    }

    if (ws && ws.readyState === WebSocket.OPEN) {
        const action = {
            type: 'game_action',
            action: {
                choice: choice
            }
        };
        console.log('Sending action:', action);
        ws.send(JSON.stringify(action));
        
        myChoice = choice;
        highlightChoice(choice);
        disableChoiceButtons();
        turnInfo.textContent = 'Choice made! Waiting for opponent...';
    } else {
        console.error('WebSocket not ready:', ws ? ws.readyState : 'null');
    }
}

function highlightChoice(choice) {
    choiceButtons.forEach(btn => {
        btn.classList.remove('selected');
        if (btn.dataset.choice === choice) {
            btn.classList.add('selected');
            btn.style.borderColor = '#ff0080';
            btn.style.color = '#ff0080';
        }
    });
}

function disableChoiceButtons() {
    choiceButtons.forEach(btn => {
        btn.disabled = true;
    });
}

function enableChoiceButtons() {
    choiceButtons.forEach(btn => {
        btn.disabled = false;
        btn.classList.remove('selected');
        btn.style.borderColor = '#00ffff';
        btn.style.color = '#00ffff';
    });
    myChoice = null;
}

function showRoundResults(data) {
    const playerIds = Object.keys(data.choices);
    const player1Id = playerIds[0];
    const player2Id = playerIds[1];

    // Show choices
    player1Choice.textContent = choiceEmojis[data.choices[player1Id]];
    player2Choice.textContent = choiceEmojis[data.choices[player2Id]];
    player1ChoiceName.textContent = players[player1Id];
    player2ChoiceName.textContent = players[player2Id];

    // Show result
    if (data.round_result === 'tie') {
        roundResult.textContent = "It's a tie!";
        roundResult.style.color = '#ffff00';
    } else {
        const winnerName = players[data.round_winner_id];
        roundResult.textContent = `${winnerName} wins this round!`;
        roundResult.style.color = '#00ff00';
    }

    // Update scores
    if (data.scores) {
        scores = data.scores;
        updateGameStatus();
    }

    // Show results container
    resultsContainer.style.display = 'block';

    // Hide results after 3 seconds and prepare for next round
    setTimeout(() => {
        resultsContainer.style.display = 'none';
        if (!data.game_complete) {
            enableChoiceButtons();
            turnInfo.textContent = 'Make your choice!';
        }
    }, 3000);

    // Check if game is complete
    if (data.game_complete) {
        setTimeout(() => {
            showGameOverResults(data);
        }, 3000);
    }
}

function showGameOverResults(data) {
    gameOver = true;
    disableChoiceButtons();

    let message = '';
    if (data.game_winner_id) {
        const winnerName = players[data.game_winner_id];
        message = `🎉 ${winnerName} wins the game! 🎉`;

        // Trigger confetti
        confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 }
        });
    } else {
        message = "It's a tie game!";
    }

    gameOverMessage.textContent = message;
    gameOverMessage.style.display = 'block';
    gameOverMessage.style.fontSize = '1.5rem';
    gameOverMessage.style.color = '#00ff00';
    gameOverMessage.style.textShadow = '0 0 20px #00ff00';
    gameOverMessage.style.marginTop = '2rem';

    turnInfo.textContent = 'Game Over!';
}

function showErrorModal(message) {
    modalTitle.textContent = 'Error';
    modalMessage.textContent = message;
    modal.style.display = 'flex';
}

function hideModal() {
    modal.style.display = 'none';
}

// Heartbeat to keep connection alive
setInterval(() => {
    if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({type: 'ping'}));
    }
}, 30000);

// Start the game when page loads
document.addEventListener('DOMContentLoaded', init);
