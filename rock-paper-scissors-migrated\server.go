package main

import (
	"context"
	"errors"
	"sync"

	gameapi "github.com/BhaumikTalwar/Amrita/service/GameApi"
)

type RockPaperScissors struct{}

func NewGame() gameapi.Game {
	return &RockPaperScissors{}
}

type RockPaperScissorsInstance struct {
	roomID        string
	players       map[string]string      // playerID -> symbol (not used but kept for consistency)
	playerNames   map[string]string      // playerID -> name
	playerChoices map[string]string      // playerID -> choice (rock/paper/scissors)
	scores        map[string]int         // playerID -> score
	currentRound  int
	maxRounds     int
	config        gameapi.GameConfig
	mu            sync.Mutex
	isGameOver    bool
	winner        string
	roundResults  []map[string]interface{} // history of round results
}

func (g *RockPaperScissors) NewInstance(config gameapi.GameConfig, roomID string) gameapi.GameInstance {
	if err := g.ValidateConfig(config); err != nil {
		return nil
	}
	
	maxRounds := 3
	if rounds, ok := config["maxRounds"].(int); ok && rounds > 0 {
		maxRounds = rounds
	}
	
	return &RockPaperScissorsInstance{
		roomID:        roomID,
		players:       make(map[string]string),
		playerNames:   make(map[string]string),
		playerChoices: make(map[string]string),
		scores:        make(map[string]int),
		currentRound:  1,
		maxRounds:     maxRounds,
		config:        config,
		isGameOver:    false,
		roundResults:  make([]map[string]interface{}, 0),
	}
}

func (g *RockPaperScissors) ValidateConfig(config gameapi.GameConfig) error {
	// Validate maxRounds if provided
	if rounds, ok := config["maxRounds"]; ok {
		if r, ok := rounds.(int); !ok || r < 1 {
			return errors.New("maxRounds must be a positive integer")
		}
	}
	return nil
}

func (g *RockPaperScissorsInstance) HandlePlayerJoin(ctx context.Context, playerID string, playerData interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.players) >= 2 {
		return errors.New("room is full")
	}

	pData, ok := playerData.(map[string]interface{})
	if !ok {
		return errors.New("invalid player data format")
	}
	name, ok := pData["username"].(string)
	if !ok {
		return errors.New("username not provided")
	}

	// Assign symbol based on join order (first player gets "X", second gets "O")
	symbol := "X"
	if len(g.players) == 1 {
		symbol = "O"
	}

	g.players[playerID] = symbol
	g.playerNames[playerID] = name
	g.scores[playerID] = 0

	return nil
}

func (g *RockPaperScissorsInstance) AddPlayer(ctx context.Context, playerID, playerName string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.players) >= 2 {
		return errors.New("room is full")
	}

	// Assign symbol based on join order (first player gets "X", second gets "O")
	symbol := "X"
	if len(g.players) == 1 {
		symbol = "O"
	}

	g.players[playerID] = symbol
	g.playerNames[playerID] = playerName
	g.scores[playerID] = 0

	return nil
}

func (g *RockPaperScissorsInstance) RemovePlayer(ctx context.Context, playerID string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if _, exists := g.players[playerID]; !exists {
		return errors.New("player not in game")
	}

	delete(g.players, playerID)
	delete(g.playerNames, playerID)
	delete(g.scores, playerID)
	delete(g.playerChoices, playerID)
	
	// End game if a player leaves
	g.isGameOver = true
	
	return nil
}



func (g *RockPaperScissorsInstance) HandlePlayerAction(ctx context.Context, playerID string, action interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.isGameOver {
		return errors.New("game is over")
	}

	if _, exists := g.players[playerID]; !exists {
		return errors.New("player not in game")
	}

	// Check if player already made a choice this round
	if _, hasChoice := g.playerChoices[playerID]; hasChoice {
		return errors.New("choice already made this round")
	}

	actionMap, ok := action.(map[string]interface{})
	if !ok {
		return errors.New("invalid action format")
	}

	choice, ok := actionMap["choice"].(string)
	if !ok {
		return errors.New("invalid choice")
	}

	// Validate choice
	if choice != "rock" && choice != "paper" && choice != "scissors" {
		return errors.New("invalid choice: must be rock, paper, or scissors")
	}

	g.playerChoices[playerID] = choice

	// Check if both players have made their choices
	if len(g.playerChoices) == 2 {
		g.processRound()
	}

	return nil
}

func (g *RockPaperScissorsInstance) HandlePlayerLeave(ctx context.Context, playerID string) error {
	return g.RemovePlayer(ctx, playerID)
}

func (g *RockPaperScissorsInstance) processRound() {
	// Determine round winner
	playerIDs := make([]string, 0, len(g.playerChoices))
	choices := make([]string, 0, len(g.playerChoices))
	
	for playerID, choice := range g.playerChoices {
		playerIDs = append(playerIDs, playerID)
		choices = append(choices, choice)
	}

	roundResult := "tie"
	var roundWinnerID string

	if len(choices) == 2 {
		choice1, choice2 := choices[0], choices[1]
		player1ID, player2ID := playerIDs[0], playerIDs[1]

		if choice1 == choice2 {
			roundResult = "tie"
		} else if (choice1 == "rock" && choice2 == "scissors") ||
			(choice1 == "paper" && choice2 == "rock") ||
			(choice1 == "scissors" && choice2 == "paper") {
			roundResult = "win"
			roundWinnerID = player1ID
			g.scores[player1ID]++
		} else {
			roundResult = "win"
			roundWinnerID = player2ID
			g.scores[player2ID]++
		}
	}

	// Store round result
	roundResultData := map[string]interface{}{
		"round":          g.currentRound,
		"choices":        g.playerChoices,
		"result":         roundResult,
		"round_winner_id": roundWinnerID,
	}
	g.roundResults = append(g.roundResults, roundResultData)

	// Check if game is over
	for _, score := range g.scores {
		if score >= (g.maxRounds+1)/2 { // Best of N logic
			g.isGameOver = true
			break
		}
	}

	// If game is over, determine the winner
	if g.isGameOver {
		var maxScore int
		for playerID, score := range g.scores {
			if score > maxScore {
				maxScore = score
				g.winner = playerID
			}
		}
	} else {
		// Prepare for next round
		g.currentRound++
		g.playerChoices = make(map[string]string)
	}
}

func (g *RockPaperScissorsInstance) GetGameState(playerID string) interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	symbol, exists := g.players[playerID]
	if !exists {
		return map[string]interface{}{
			"error": "player not in game",
		}
	}

	return map[string]interface{}{
		"symbol":       symbol,
		"players":      g.playerNames,
		"scores":       g.scores,
		"currentRound": g.currentRound,
		"maxRounds":    g.maxRounds,
		"isGameOver":   g.isGameOver,
		"winner":       g.winner,
	}
}

func (g *RockPaperScissorsInstance) GetPublicState() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	// For public state, don't show choices unless round is complete
	visibleChoices := make(map[string]string)
	if len(g.playerChoices) == 2 {
		visibleChoices = g.playerChoices
	} else {
		// Just show who has made a choice
		for id := range g.playerChoices {
			visibleChoices[id] = "made_choice"
		}
	}

	return map[string]interface{}{
		"currentRound":   g.currentRound,
		"maxRounds":      g.maxRounds,
		"playerChoices":  visibleChoices,
		"scores":         g.scores,
		"players":        g.playerNames,
		"isGameOver":     g.isGameOver,
		"winner":         g.winner,
		"roundComplete":  len(g.playerChoices) == 2,
	}
}

func (g *RockPaperScissorsInstance) IsGameOver() bool {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.isGameOver
}

func (g *RockPaperScissorsInstance) GetWinner() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.winner
}

func (g *RockPaperScissorsInstance) Cleanup() error {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.players = make(map[string]string)
	g.playerNames = make(map[string]string)
	g.playerChoices = make(map[string]string)
	g.scores = make(map[string]int)
	g.currentRound = 1
	g.isGameOver = false
	g.winner = ""
	g.roundResults = make([]map[string]interface{}, 0)
	return nil
}

func (g *RockPaperScissorsInstance) GetRoundResult() map[string]interface{} {
	if len(g.roundResults) == 0 {
		return nil
	}

	lastResult := g.roundResults[len(g.roundResults)-1]

	return map[string]interface{}{
		"round_result":    lastResult["result"],
		"round_winner_id": lastResult["round_winner_id"],
		"choices":         lastResult["choices"],
		"scores":          g.scores,
		"current_round":   g.currentRound,
		"game_complete":   g.isGameOver,
		"game_winner_id":  g.winner,
		"players":         g.playerNames,
	}
}
