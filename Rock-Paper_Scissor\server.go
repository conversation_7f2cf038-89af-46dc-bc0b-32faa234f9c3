package main

import (
	"context"
	"errors"
	"sync"

	gameapi "github.com/BhaumikTalwar/Amrita/service/GameApi"
)

type RockPaperScissors struct{}

func NewGame() gameapi.Game {
	return &RockPaperScissors{}
}

type RockPaperScissorsInstance struct {
	roomID        string
	players       map[string]string      // playerID -> symbol (not used but kept for consistency)
	playerNames   map[string]string      // playerID -> name
	playerChoices map[string]string      // playerID -> choice (rock/paper/scissors)
	scores        map[string]int         // playerID -> score
	currentRound  int
	maxRounds     int
	config        gameapi.GameConfig
	mu            sync.Mutex
	isGameOver    bool
	winner        string
	roundResults  []map[string]interface{} // history of round results
}

func (g *RockPaperScissors) NewInstance(config gameapi.GameConfig, roomID string) gameapi.GameInstance {
	if err := g.ValidateConfig(config); err != nil {
		return nil
	}
	
	maxRounds := 3
	if rounds, ok := config["maxRounds"].(int); ok && rounds > 0 {
		maxRounds = rounds
	}
	
	return &RockPaperScissorsInstance{
		roomID:        roomID,
		players:       make(map[string]string),
		playerNames:   make(map[string]string),
		playerChoices: make(map[string]string),
		scores:        make(map[string]int),
		currentRound:  1,
		maxRounds:     maxRounds,
		config:        config,
		isGameOver:    false,
		roundResults:  make([]map[string]interface{}, 0),
	}
}

func (g *RockPaperScissors) ValidateConfig(config gameapi.GameConfig) error {
	// Validate maxRounds if provided
	if rounds, ok := config["maxRounds"]; ok {
		if r, ok := rounds.(int); !ok || r < 1 {
			return errors.New("maxRounds must be a positive integer")
		}
	}
	return nil
}

func (g *RockPaperScissorsInstance) HandlePlayerJoin(ctx context.Context, playerID string, playerData interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.players) >= 2 {
		return errors.New("game is full")
	}

	pData, ok := playerData.(map[string]interface{})
	if !ok {
		return errors.New("invalid player data format")
	}
	name, ok := pData["username"].(string)
	if !ok {
		return errors.New("username not provided")
	}

	// In RPS, we don't need symbols like X/O, but we'll keep the field for consistency
	g.players[playerID] = playerID
	g.playerNames[playerID] = name
	g.scores[playerID] = 0

	return nil
}

func (g *RockPaperScissorsInstance) HandlePlayerAction(ctx context.Context, playerID string, action interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.isGameOver {
		return errors.New("game is over")
	}

	_, exists := g.players[playerID]
	if !exists {
		return errors.New("player not in game")
	}

	// Check if player already made a choice for this round
	if _, alreadyChose := g.playerChoices[playerID]; alreadyChose {
		return errors.New("already made a choice for this round")
	}

	move, ok := action.(map[string]interface{})
	if !ok {
		return errors.New("invalid action format")
	}

	choice, ok := move["choice"].(string)
	if !ok {
		return errors.New("invalid choice")
	}

	// Validate choice
	if choice != "rock" && choice != "paper" && choice != "scissors" {
		return errors.New("invalid choice: must be rock, paper, or scissors")
	}

	// Record player's choice
	g.playerChoices[playerID] = choice

	// If both players have made their choices, determine the round winner
	if len(g.playerChoices) == 2 {
		g.determineRoundResult()
	}

	return nil
}

func (g *RockPaperScissorsInstance) determineRoundResult() {
	// Get both players' IDs and choices
	var playerIDs []string
	for id := range g.players {
		playerIDs = append(playerIDs, id)
	}

	player1ID := playerIDs[0]
	player2ID := playerIDs[1]
	player1Choice := g.playerChoices[player1ID]
	player2Choice := g.playerChoices[player2ID]

	// Determine winner
	var roundWinnerID string
	var roundResult string

	if player1Choice == player2Choice {
		// Tie
		roundResult = "tie"
	} else if (player1Choice == "rock" && player2Choice == "scissors") ||
		(player1Choice == "paper" && player2Choice == "rock") ||
		(player1Choice == "scissors" && player2Choice == "paper") {
		// Player 1 wins
		roundWinnerID = player1ID
		roundResult = "win"
		g.scores[player1ID]++
	} else {
		// Player 2 wins
		roundWinnerID = player2ID
		roundResult = "win"
		g.scores[player2ID]++
	}

	// Store round result
	roundResultData := map[string]interface{}{
		"round":          g.currentRound,
		"choices":        g.playerChoices,
		"result":         roundResult,
		"round_winner_id": roundWinnerID,
	}
	g.roundResults = append(g.roundResults, roundResultData)

	// Check if game is over
	for _, score := range g.scores {
		if score >= (g.maxRounds+1)/2 { // Best of N logic
			g.isGameOver = true
			break
		}
	}

	// If game is over, determine the winner
	if g.isGameOver {
		var maxScore int
		for playerID, score := range g.scores {
			if score > maxScore {
				maxScore = score
				g.winner = playerID
			}
		}
	} else {
		// Prepare for next round
		g.currentRound++
		g.playerChoices = make(map[string]string)
	}
}

func (g *RockPaperScissorsInstance) HandlePlayerLeave(ctx context.Context, playerID string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	_, exists := g.players[playerID]
	if !exists {
		return errors.New("player not in game")
	}

	delete(g.players, playerID)
	delete(g.playerNames, playerID)
	delete(g.scores, playerID)
	delete(g.playerChoices, playerID)
	g.isGameOver = true
	return nil
}

func (g *RockPaperScissorsInstance) GetGameState(playerID string) interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	_, exists := g.players[playerID]
	if !exists {
		return map[string]interface{}{
			"error": "player not in game",
		}
	}

	// Create a copy of player choices, but only show the current player's choice
	visibleChoices := make(map[string]string)
	for id, choice := range g.playerChoices {
		if id == playerID {
			visibleChoices[id] = choice
		} else {
			// Only show opponent's choice if round is complete
			if len(g.playerChoices) == 2 {
				visibleChoices[id] = choice
			} else {
				visibleChoices[id] = "pending"
			}
		}
	}

	return map[string]interface{}{
		"currentRound":  g.currentRound,
		"maxRounds":     g.maxRounds,
		"playerChoices":  visibleChoices,
		"scores":        g.scores,
		"players":       g.playerNames,
		"isGameOver":    g.isGameOver,
		"winner":        g.winner,
		"roundComplete": len(g.playerChoices) == 2,
		"roundResults":  g.getRoundResults(),
	}
}

func (g *RockPaperScissorsInstance) GetPublicState() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	// For public state, don't show choices unless round is complete
	visibleChoices := make(map[string]string)
	if len(g.playerChoices) == 2 {
		visibleChoices = g.playerChoices
	} else {
		// Just show who has made a choice
		for id := range g.playerChoices {
			visibleChoices[id] = "made_choice"
		}
	}

	return map[string]interface{}{
		"currentRound":  g.currentRound,
		"maxRounds":     g.maxRounds,
		"playerChoices":  visibleChoices,
		"scores":        g.scores,
		"players":       g.playerNames,
		"isGameOver":    g.isGameOver,
		"winner":        g.winner,
		"roundComplete": len(g.playerChoices) == 2,
	}
}

func (g *RockPaperScissorsInstance) IsGameOver() bool {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.isGameOver
}

func (g *RockPaperScissorsInstance) GetWinner() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.winner
}

func (g *RockPaperScissorsInstance) Cleanup() error {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.players = make(map[string]string)
	g.playerNames = make(map[string]string)
	g.playerChoices = make(map[string]string)
	g.scores = make(map[string]int)
	g.currentRound = 1
	g.isGameOver = false
	g.winner = ""
	g.roundResults = make([]map[string]interface{}, 0)
	return nil
}

func (g *RockPaperScissorsInstance) getRoundResults() []map[string]interface{} {
	// Return only completed rounds
	if len(g.playerChoices) < 2 {
		return g.roundResults
	}
	
	// Include current round if complete
	return g.roundResults
}
