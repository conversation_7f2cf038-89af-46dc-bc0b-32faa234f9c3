package main

import (
	"context"
	"errors"
	"sync"

	gameapi "github.com/BhaumikTalwar/Amrita/service/GameApi"
)

type TicTacToe struct{}

func NewGame() gameapi.Game {
	return &TicTacToe{}
}

type TicTacToeInstance struct {
	roomID      string
	board       [7][7]string
	players     map[string]string
	playerNames map[string]string
	currentTurn string
	config      gameapi.GameConfig
	mu          sync.Mutex
	isGameOver  bool
	winner      string
}

func (g *TicTacToe) NewInstance(config gameapi.GameConfig, roomID string) gameapi.GameInstance {
	if err := g.ValidateConfig(config); err != nil {
		return nil
	}
	return &TicTacToeInstance{
		roomID:      roomID,
		players:     make(map[string]string),
		playerNames: make(map[string]string),
		config:      config,
		isGameOver:  false,
	}
}

func (g *TicTacToe) ValidateConfig(config gameapi.GameConfig) error {
	if size, ok := config["boardSize"]; ok {
		if s, ok := size.(int); !ok || s != 7 {
			return errors.New("boardSize must be 7")
		}
	}
	if winLength, ok := config["winLength"]; ok {
		if w, ok := winLength.(int); !ok || w < 3 || w > 7 {
			return errors.New("winLength must be an integer between 3 and 7")
		}
	}
	return nil
}

func (g *TicTacToeInstance) HandlePlayerJoin(ctx context.Context, playerID string, playerData interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.players) >= 2 {
		return errors.New("game is full")
	}

	pData, ok := playerData.(map[string]interface{})
	if !ok {
		return errors.New("invalid player data format")
	}
	name, ok := pData["username"].(string)
	if !ok {
		return errors.New("username not provided")
	}

	symbol := "X"
	if len(g.players) == 1 {
		for _, s := range g.players {
			if s == "X" {
				symbol = "O"
			}
		}
	}

	g.players[playerID] = symbol
	g.playerNames[symbol] = name

	if len(g.players) == 2 {
		g.currentTurn = "X"
	}

	return nil
}

func (g *TicTacToeInstance) HandlePlayerAction(ctx context.Context, playerID string, action interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.isGameOver {
		return errors.New("game is over")
	}

	symbol, exists := g.players[playerID]
	if !exists {
		return errors.New("player not in game")
	}
	if symbol != g.currentTurn {
		return errors.New("not your turn")
	}

	move, ok := action.(map[string]interface{})
	if !ok {
		return errors.New("invalid action format")
	}

	row, ok := move["row"].(float64)
	if !ok {
		return errors.New("invalid row")
	}
	col, ok := move["col"].(float64)
	if !ok {
		return errors.New("invalid col")
	}

	r, c := int(row), int(col)
	if r < 0 || r >= 7 || c < 0 || c >= 7 {
		return errors.New("move out of bounds")
	}
	if g.board[r][c] != "" {
		return errors.New("cell already occupied")
	}

	g.board[r][c] = symbol

	if g.checkWin(r, c, symbol) {
		g.isGameOver = true
		g.winner = symbol
		return nil
	}

	if g.checkDraw() {
		g.isGameOver = true
		return nil
	}

	g.currentTurn = "O"
	if symbol == "O" {
		g.currentTurn = "X"
	}

	return nil
}

func (g *TicTacToeInstance) HandlePlayerLeave(ctx context.Context, playerID string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	symbol, exists := g.players[playerID]
	if !exists {
		return errors.New("player not in game")
	}

	delete(g.players, playerID)
	delete(g.playerNames, symbol)
	g.isGameOver = true
	return nil
}

func (g *TicTacToeInstance) GetGameState(playerID string) interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	symbol, exists := g.players[playerID]
	if !exists {
		return map[string]interface{}{
			"error": "player not in game",
		}
	}

	return map[string]interface{}{
		"board":       g.board,
		"symbol":      symbol,
		"currentTurn": g.currentTurn,
		"players":     g.playerNames,
		"isGameOver":  g.isGameOver,
		"winner":      g.winner,
	}
}

func (g *TicTacToeInstance) GetPublicState() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	return map[string]interface{}{
		"board":       g.board,
		"currentTurn": g.currentTurn,
		"players":     g.playerNames,
		"isGameOver":  g.isGameOver,
		"winner":      g.winner,
	}
}

func (g *TicTacToeInstance) IsGameOver() bool {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.isGameOver
}

func (g *TicTacToeInstance) GetWinner() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.winner
}

func (g *TicTacToeInstance) Cleanup() error {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.board = [7][7]string{}
	g.players = make(map[string]string)
	g.playerNames = make(map[string]string)
	g.currentTurn = ""
	g.isGameOver = false
	g.winner = ""
	return nil
}

func (g *TicTacToeInstance) checkWin(row, col int, symbol string) bool {
	winLength := 5
	if wl, ok := g.config["winLength"].(int); ok {
		winLength = wl
	}

	// Row
	count := 0
	for c := 0; c < 7; c++ {
		if g.board[row][c] == symbol {
			count++
			if count >= winLength {
				return true
			}
		} else {
			count = 0
		}
	}

	// Column
	count = 0
	for r := 0; r < 7; r++ {
		if g.board[r][col] == symbol {
			count++
			if count >= winLength {
				return true
			}
		} else {
			count = 0
		}
	}

	// Diagonal (\)
	count = 0
	for i := -6; i <= 6; i++ {
		r, c := row+i, col+i
		if r >= 0 && r < 7 && c >= 0 && c < 7 {
			if g.board[r][c] == symbol {
				count++
				if count >= winLength {
					return true
				}
			} else {
				count = 0
			}
		}
	}

	// Anti-diagonal (/)
	count = 0
	for i := -6; i <= 6; i++ {
		r, c := row+i, col-i
		if r >= 0 && r < 7 && c >= 0 && c < 7 {
			if g.board[r][c] == symbol {
				count++
				if count >= winLength {
					return true
				}
			} else {
				count = 0
			}
		}
	}

	return false
}

func (g *TicTacToeInstance) checkDraw() bool {
	for i := 0; i < 7; i++ {
		for j := 0; j < 7; j++ {
			if g.board[i][j] == "" {
				return false
			}
		}
	}
	return true
}
