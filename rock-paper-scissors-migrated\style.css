/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Press Start 2P', 'Courier New', monospace;
    background:
        linear-gradient(45deg, #2a0845 0%, #4a1a5c 25%, #6b2c7a 50%, #8b3f99 75%, #ab52b8 100%),
        repeating-linear-gradient(
            90deg,
            transparent 0px,
            transparent 2px,
            rgba(255, 255, 255, 0.03) 2px,
            rgba(255, 255, 255, 0.03) 4px
        );
    min-height: 100vh;
    color: #00ffff;
    overflow-x: hidden;
    position: relative;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

/* 90s Grid Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 1;
    animation: gridMove 20s linear infinite;
}

/* Moving grid animation */
@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
}

/* 90s Starfield Effect */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 25px 35px, #ffffff, transparent),
        radial-gradient(1px 1px at 85px 15px, #ffff00, transparent),
        radial-gradient(1px 1px at 155px 95px, #ff00ff, transparent),
        radial-gradient(1px 1px at 195px 45px, #00ff00, transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: starfield 15s linear infinite;
    pointer-events: none;
    z-index: 2;
    opacity: 0.7;
}

@keyframes starfield {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-200px); }
}

.font-retro {
    font-family: 'Press Start 2P', monospace;
}

.font-fredoka {
    font-family: 'Fredoka', sans-serif;
}

/* Loading Screen Styles - Exact copy from Tic Tac Toe */
@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.logo-float {
    animation: float 4s ease-in-out infinite;
}

@keyframes shine {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.progress-bar-shine::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 60%);
    background-size: 200% 100%;
    animation: shine 2s infinite linear;
    z-index: 1;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 1s ease-out forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

.text-glow {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.1);
}

.glow-bar {
    box-shadow: 0 0 12px rgba(255, 186, 60, 0.5), 0 0 20px rgba(255, 115, 40, 0.3);
}

#loadingScreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

/* Game UI Styles */
#gameUI {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.5s ease-in, visibility 0.5s;
    min-height: 100vh;
    padding: 2rem;
    position: relative;
    z-index: 10;
}

#gameUI.active {
    visibility: visible;
    opacity: 1;
}

.game-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

.game-title {
    font-family: 'Press Start 2P', monospace;
    font-size: 2.2rem;
    margin-bottom: 1rem;
    color: #ffff00;
    text-shadow:
        3px 3px 0px #ff00ff,
        6px 6px 0px #00ffff,
        9px 9px 0px #ff0080;
    position: relative;
    letter-spacing: 2px;
    animation: neon90s 2s ease-in-out infinite alternate;
    z-index: 10;
    text-align: center;
}

.game-title::before {
    content: '★ ';
    color: #ff0080;
    animation: starSpin 3s linear infinite;
}

.game-title::after {
    content: ' ★';
    color: #ff0080;
    animation: starSpin 3s linear infinite reverse;
}

@keyframes neon90s {
    0% {
        text-shadow:
            3px 3px 0px #ff00ff,
            6px 6px 0px #00ffff,
            9px 9px 0px #ff0080,
            0 0 20px #ffff00;
    }
    100% {
        text-shadow:
            3px 3px 0px #ff00ff,
            6px 6px 0px #00ffff,
            9px 9px 0px #ff0080,
            0 0 40px #ffff00,
            0 0 60px #ffff00;
    }
}

@keyframes starSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

header {
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    z-index: 10;
}

header p {
    font-family: 'Press Start 2P', monospace;
    font-size: 0.8rem;
    color: #00ffff;
    margin-top: 1rem;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #00ffff;
    animation: textBlink 2s ease-in-out infinite alternate;
}

.players-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background:
        repeating-linear-gradient(
            45deg,
            #000000 0px,
            #000000 2px,
            #111111 2px,
            #111111 4px
        ),
        linear-gradient(135deg, #1a0033 0%, #330066 100%);
    border: 4px solid #00ffff;
    border-radius: 0;
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 4px #00ffff,
        0 0 20px #00ffff,
        0 8px 16px rgba(0, 0, 0, 0.8);
    position: relative;
    overflow: hidden;
}

.player-info {
    font-size: 0.9rem;
    color: #00ffff;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #00ffff;
    animation: textBlink 1.5s ease-in-out infinite alternate;
}

.player-name {
    color: #ff0080;
    text-shadow:
        2px 2px 0px #000000,
        0 0 15px #ff0080;
}

@keyframes textBlink {
    0% {
        opacity: 0.8;
        text-shadow:
            2px 2px 0px #000000,
            0 0 10px currentColor;
    }
    100% {
        opacity: 1;
        text-shadow:
            2px 2px 0px #000000,
            0 0 20px currentColor;
    }
}

.game-status {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background:
        repeating-linear-gradient(
            45deg,
            #000000 0px,
            #000000 2px,
            #111111 2px,
            #111111 4px
        ),
        linear-gradient(135deg, #1a0033 0%, #330066 100%);
    border: 4px solid #00ffff;
    border-radius: 0;
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 4px #00ffff,
        0 0 20px #00ffff,
        0 8px 16px rgba(0, 0, 0, 0.8);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.game-status div {
    color: #00ff00;
    font-size: 0.9rem;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #00ff00;
    margin: 0.5rem 0;
    animation: statusGlow 2s ease-in-out infinite alternate;
}

@keyframes statusGlow {
    from {
        text-shadow: 0 0 20px rgba(100, 255, 218, 0.8);
        box-shadow:
            0 0 30px rgba(0, 245, 255, 0.3),
            inset 0 0 30px rgba(255, 255, 255, 0.05);
    }
    to {
        text-shadow: 0 0 30px rgba(100, 255, 218, 1);
        box-shadow:
            0 0 50px rgba(0, 245, 255, 0.5),
            inset 0 0 30px rgba(255, 255, 255, 0.1);
    }
}

/* Timer Display */
.timer-display {
    font-size: 1rem;
    color: #00ffff;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #00ffff;
    margin-top: 0.5rem;
    font-weight: bold;
    font-family: 'Press Start 2P', monospace;
}

.timer-warning {
    color: #ff4000 !important;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #ff4000 !important;
    animation: timerBlink 0.5s ease-in-out infinite alternate;
}

@keyframes timerBlink {
    0% {
        opacity: 0.7;
        text-shadow:
            2px 2px 0px #000000,
            0 0 10px #ff4000;
    }
    100% {
        opacity: 1;
        text-shadow:
            2px 2px 0px #000000,
            0 0 20px #ff4000,
            0 0 30px #ff4000;
    }
}

.choices-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.choice-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem 1.5rem;
    border: 4px solid #00ffff;
    border-radius: 0;
    background:
        linear-gradient(135deg, #1a0033 0%, #330066 100%),
        repeating-linear-gradient(
            45deg,
            transparent 0px,
            transparent 4px,
            rgba(0, 255, 255, 0.1) 4px,
            rgba(0, 255, 255, 0.1) 8px
        );
    cursor: pointer;
    transition: all 0.1s ease;
    font-family: 'Press Start 2P', monospace;
    font-size: 0.7rem;
    color: #00ffff;
    position: relative;
    overflow: hidden;
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 6px #00ffff,
        6px 6px 0px #000000,
        0 0 20px #00ffff;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #00ffff;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.choice-btn:hover {
    transform: translate(-2px, -2px);
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 6px #ff0080,
        8px 8px 0px #000000,
        0 0 30px #ff0080;
    border-color: #ff0080;
    color: #ff0080;
    text-shadow:
        2px 2px 0px #000000,
        0 0 15px #ff0080;
}

.choice-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.choice-btn.selected {
    border-color: #ff0080;
    color: #ff0080;
    text-shadow:
        2px 2px 0px #000000,
        0 0 20px #ff0080;
    transform: translate(-2px, -2px);
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 6px #ff0080,
        8px 8px 0px #000000,
        0 0 40px #ff0080;
    animation: retro90sPulse 1s ease-in-out infinite alternate;
}

@keyframes retro90sPulse {
    0% {
        box-shadow:
            inset 0 0 0 2px #000000,
            inset 0 0 0 6px #ff0080,
            8px 8px 0px #000000,
            0 0 40px #ff0080;
    }
    100% {
        box-shadow:
            inset 0 0 0 2px #000000,
            inset 0 0 0 6px #ff0080,
            8px 8px 0px #000000,
            0 0 60px #ff0080;
    }
}

.choice-emoji {
    font-size: 3.5rem;
    filter:
        drop-shadow(3px 3px 0px #000000)
        drop-shadow(0 0 10px currentColor);
    transition: all 0.1s ease;
    image-rendering: pixelated;
}

.choice-name {
    font-size: 0.8rem;
}

/* Results Display */
.results-container {
    display: none;
    background:
        repeating-linear-gradient(
            45deg,
            #000000 0px,
            #000000 2px,
            #111111 2px,
            #111111 4px
        ),
        linear-gradient(135deg, #1a0033 0%, #330066 100%);
    border: 4px solid #00ffff;
    border-radius: 0;
    padding: 2rem;
    margin: 2rem auto;
    max-width: 500px;
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 4px #00ffff,
        0 0 20px #00ffff,
        0 8px 16px rgba(0, 0, 0, 0.8);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.round-result {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-shadow:
        2px 2px 0px #000000,
        0 0 15px currentColor;
    font-family: 'Press Start 2P', monospace;
    animation: resultPulse 1s ease-in-out infinite alternate;
}

@keyframes resultPulse {
    0% {
        text-shadow:
            2px 2px 0px #000000,
            0 0 15px currentColor;
    }
    100% {
        text-shadow:
            2px 2px 0px #000000,
            0 0 25px currentColor,
            0 0 35px currentColor;
    }
}

.choices-display {
    display: flex;
    justify-content: space-around;
    margin: 2rem 0;
}

.player-choice {
    text-align: center;
    color: #00ffff;
    font-family: 'Press Start 2P', monospace;
    font-size: 0.8rem;
}

.choice-display-emoji {
    font-size: 4rem;
    display: block;
    margin-bottom: 0.5rem;
    filter:
        drop-shadow(3px 3px 0px #000000)
        drop-shadow(0 0 15px #00ffff);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background:
        repeating-linear-gradient(
            45deg,
            #000000 0px,
            #000000 2px,
            #111111 2px,
            #111111 4px
        ),
        linear-gradient(135deg, #1a0033 0%, #330066 100%);
    padding: 2rem;
    border: 4px solid #00ffff;
    border-radius: 0;
    text-align: center;
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 4px #00ffff,
        0 0 30px #00ffff,
        0 8px 16px rgba(0, 0, 0, 0.8);
    color: #00ffff;
    max-width: 400px;
    margin: 0 1rem;
    position: relative;
    overflow: hidden;
}

.modal-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-shadow:
        2px 2px 0px #000000,
        0 0 15px #00ffff;
    font-family: 'Press Start 2P', monospace;
    color: #ffff00;
}

.modal-message {
    font-size: 0.9rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    font-family: 'Press Start 2P', monospace;
    line-height: 1.6;
}

.modal-button {
    background:
        linear-gradient(135deg, #1a0033 0%, #330066 100%),
        repeating-linear-gradient(
            45deg,
            transparent 0px,
            transparent 4px,
            rgba(0, 255, 255, 0.1) 4px,
            rgba(0, 255, 255, 0.1) 8px
        );
    border: 4px solid #00ffff;
    border-radius: 0;
    color: #00ffff;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    font-family: 'Press Start 2P', monospace;
    font-size: 0.7rem;
    transition: all 0.1s ease;
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 6px #00ffff,
        4px 4px 0px #000000,
        0 0 15px #00ffff;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #00ffff;
}

.modal-button:hover {
    transform: translate(-2px, -2px);
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 6px #ff0080,
        6px 6px 0px #000000,
        0 0 20px #ff0080;
    border-color: #ff0080;
    color: #ff0080;
    text-shadow:
        2px 2px 0px #000000,
        0 0 15px #ff0080;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 1.8rem;
    }
    
    .loading-title {
        font-size: 1.8rem;
    }
    
    .choices-container {
        grid-template-columns: 1fr;
        max-width: 300px;
    }
    
    .choice-btn {
        padding: 1.5rem 1rem;
    }
    
    .choice-emoji {
        font-size: 2.5rem;
    }
    
    .players-info {
        flex-direction: column;
        gap: 1rem;
    }
}
