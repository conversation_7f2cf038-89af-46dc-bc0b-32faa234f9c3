/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Press Start 2P', 'Courier New', monospace;
    margin: 0;
    overflow: hidden;
    background:
        linear-gradient(45deg, #2a0845 0%, #4a1a5c 25%, #6b2c7a 50%, #8b3f99 75%, #ab52b8 100%),
        repeating-linear-gradient(
            90deg,
            transparent 0px,
            transparent 2px,
            rgba(255, 255, 255, 0.03) 2px,
            rgba(255, 255, 255, 0.03) 4px
        );
    color: #00ffff;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

.font-retro {
    font-family: 'Press Start 2P', monospace;
}

.font-fredoka {
    font-family: 'Fredoka', sans-serif;
}

/* Loading Screen Styles - Exact copy from <PERSON><PERSON><PERSON> */
@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.logo-float {
    animation: float 4s ease-in-out infinite;
}

@keyframes shine {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.progress-bar-shine::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 60%);
    background-size: 200% 100%;
    animation: shine 2s infinite linear;
    z-index: 1;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 1s ease-out forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

.text-glow {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.1);
}

.glow-bar {
    box-shadow: 0 0 12px rgba(255, 186, 60, 0.5), 0 0 20px rgba(255, 115, 40, 0.3);
}

#loadingScreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

/* Game UI Styles */
#gameUI {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.5s ease-in, visibility 0.5s;
    min-height: 100vh;
    padding: 2rem;
}

#gameUI.active {
    visibility: visible;
    opacity: 1;
}

.game-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.game-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-shadow: 0 0 20px #00ffff;
    animation: glow 2s ease-in-out infinite alternate;
}

.players-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid #00ffff;
    border-radius: 8px;
}

.player-info {
    font-size: 1rem;
}

.player-name {
    color: #ff0080;
}

.game-status {
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid #00ffff;
    border-radius: 8px;
    font-size: 1rem;
}

.choices-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.choice-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem 1.5rem;
    border: 4px solid #00ffff;
    border-radius: 8px;
    background: linear-gradient(135deg, #1a0033 0%, #330066 100%);
    cursor: pointer;
    transition: all 0.1s ease;
    font-family: 'Press Start 2P', monospace;
    font-size: 0.7rem;
    color: #00ffff;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    text-shadow: 0 0 10px #00ffff;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.choice-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    border-color: #ff0080;
    color: #ff0080;
    text-shadow: 0 0 15px #ff0080;
}

.choice-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.choice-btn.selected {
    border-color: #ff0080;
    color: #ff0080;
    text-shadow: 0 0 20px #ff0080;
    transform: translateY(-2px);
    box-shadow: 0 0 40px rgba(255, 0, 128, 0.5);
    animation: retro90sPulse 1s ease-in-out infinite alternate;
}

@keyframes retro90sPulse {
    0% {
        box-shadow: 0 0 40px rgba(255, 0, 128, 0.5);
    }
    100% {
        box-shadow: 0 0 60px rgba(255, 0, 128, 0.8);
    }
}

.choice-emoji {
    font-size: 3.5rem;
    filter: drop-shadow(3px 3px 0px #000000) drop-shadow(0 0 10px currentColor);
    transition: all 0.1s ease;
    image-rendering: pixelated;
}

.choice-name {
    font-size: 0.8rem;
}

/* Results Display */
.results-container {
    display: none;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #00ffff;
    border-radius: 8px;
    padding: 2rem;
    margin: 2rem auto;
    max-width: 500px;
}

.round-result {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-shadow: 0 0 15px currentColor;
}

.choices-display {
    display: flex;
    justify-content: space-around;
    margin: 2rem 0;
}

.player-choice {
    text-align: center;
}

.choice-display-emoji {
    font-size: 4rem;
    display: block;
    margin-bottom: 0.5rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: linear-gradient(135deg, #4c1d95, #1e1b4b);
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
    border: 2px solid #00ffff;
    color: #00ffff;
    max-width: 400px;
    margin: 0 1rem;
}

.modal-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-shadow: 0 0 15px #00ffff;
}

.modal-message {
    font-size: 1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.modal-button {
    background: linear-gradient(135deg, #1a0033 0%, #330066 100%);
    border: 2px solid #00ffff;
    color: #00ffff;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Press Start 2P', monospace;
    font-size: 0.7rem;
    transition: all 0.2s ease;
}

.modal-button:hover {
    background: linear-gradient(135deg, #330066 0%, #1a0033 100%);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 1.8rem;
    }
    
    .loading-title {
        font-size: 1.8rem;
    }
    
    .choices-container {
        grid-template-columns: 1fr;
        max-width: 300px;
    }
    
    .choice-btn {
        padding: 1.5rem 1rem;
    }
    
    .choice-emoji {
        font-size: 2.5rem;
    }
    
    .players-info {
        flex-direction: column;
        gap: 1rem;
    }
}
