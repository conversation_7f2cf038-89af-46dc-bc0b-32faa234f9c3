<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GamyDay Games - <PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Fredoka:wght@400;600;700&family=Inter:wght@400;500;700&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            overflow: hidden;
        }

        .font-fredoka {
            font-family: 'Fredoka', sans-serif;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .logo-float {
            animation: float 4s ease-in-out infinite;
        }

        @keyframes shine {
            0% {
                background-position: 200% 0;
            }

            100% {
                background-position: -200% 0;
            }
        }

        .progress-bar-shine::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 60%);
            background-size: 200% 100%;
            animation: shine 2s infinite linear;
            z-index: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 1s ease-out forwards;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }

            to {
                opacity: 0;
            }
        }

        .fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }

        .text-glow {
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .glow-bar {
            box-shadow: 0 0 12px rgba(255, 186, 60, 0.5), 0 0 20px rgba(255, 115, 40, 0.3);
        }

        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        #gameUI {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.5s ease-in, visibility 0.5s;
        }

        #gameUI.active {
            visibility: visible;
            opacity: 1;
        }

        .cell {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            user-select: none;
            width: 100%;
            height: 100%;
            min-height: 70px;
        }

        .cell:hover:not(.occupied) {
            background-color: rgba(255, 255, 255, 0.1);
            transform: scale(1.05);
        }

        .cell.occupied {
            cursor: not-allowed;
        }

        .cell.x {
            color: #38bdf8;
            text-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
        }

        .cell.o {
            color: #f97316;
            text-shadow: 0 0 10px rgba(249, 115, 22, 0.5);
        }

        #modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        #modal.show {
            display: flex;
        }

        .modal-content {
            background: linear-gradient(135deg, #4c1d95, #1e1b4b);
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
            transform: scale(0.7);
            animation: modalPop 0.3s ease-out forwards;
        }

        @keyframes modalPop {
            to {
                transform: scale(1);
            }
        }

        #gameBoard {
            background: radial-gradient(circle at center, #1f2937 0%, #111827 100%);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.05), inset 0 0 10px rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.05);
        }

        #turnIndicator {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(6px);
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.05);
        }

        @keyframes pulse {

            0%,
            100% {
                text-shadow: 0 0 4px rgba(251, 191, 36, 0.7);
            }

            50% {
                text-shadow: 0 0 10px rgba(251, 191, 36, 1);
            }
        }

        #countdownTimer {
            animation: pulse 1s infinite;
            display: inline-block;
        }
    </style>
</head>

<body class="bg-gray-900">
    <div id="loadingScreen"
        class="fade-in-up flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-[#4c1d95] via-[#6b21a8] to-[#1e1b4b] p-6 text-white">
        <div class="mb-8 logo-float">
            <svg width="120" height="120" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="logoGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#38bdf8" />
                        <stop offset="100%" style="stop-color:#6366f1" />
                    </linearGradient>
                    <linearGradient id="logoGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#fbbf24" />
                        <stop offset="100%" style="stop-color:#f97316" />
                    </linearGradient>
                    <linearGradient id="logoGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ec4899" />
                        <stop offset="100%" style="stop-color:#d946ef" />
                    </linearGradient>
                </defs>
                <g transform="rotate(15 100 100)">
                    <path
                        d="M100 20 C144.18 20 180 55.82 180 100 C180 144.18 144.18 180 100 180 C55.82 180 20 144.18 20 100"
                        fill="none" stroke="url(#logoGradient1)" stroke-width="20" stroke-linecap="round" />
                    <path d="M100 20 C55.82 20 20 55.82 20 100 C20 144.18 55.82 180 100 180" fill="none"
                        stroke="url(#logoGradient2)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(120 100 100)" />
                    <path d="M20 100 C20 55.82 55.82 20 100 20 C144.18 20 180 55.82 180 100" fill="none"
                        stroke="url(#logoGradient3)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(240 100 100)" />
                    <circle cx="100" cy="100" r="25" fill="white" />
                </g>
            </svg>
        </div>
        <h1 class="font-fredoka text-4xl md:text-5xl font-bold text-center text-white tracking-wider text-glow mb-6">
            GamyDay Games
        </h1>
        <div class="w-full max-w-md mt-4 mb-8">
            <div class="h-4 bg-white/10 rounded-full overflow-hidden shadow-inner relative">
                <div id="progressBar"
                    class="h-full bg-gradient-to-r from-amber-400 to-orange-500 rounded-full transition-all duration-500 ease-out relative progress-bar-shine glow-bar"
                    style="width: 0%;"></div>
            </div>
            <p id="loadingMessage" class="text-center mt-4 text-sm text-gray-300 h-5 font-medium tracking-wide">
                Initializing...
            </p>
        </div>
        <div class="absolute bottom-6 text-center text-sm text-gray-400">
            powered by <span class="font-semibold text-white">Amrita</span>
        </div>
    </div>

    <div id="gameUI" class="flex flex-col items-center justify-center min-h-screen bg-gray-900 text-white p-4">
        <h1 class="font-fredoka text-3xl md:text-4xl font-bold mb-4 text-glow">7x7 Tic-Tac-Toe</h1>
        <div class="flex justify-between w-full max-w-md mb-4">
            <div id="player1" class="text-lg font-semibold">
                <span class="text-blue-400">Player 1 (X):</span> <span id="player1Name">Waiting...</span>
            </div>
            <div id="player2" class="text-lg font-semibold">
                <span class="text-orange-400">Player 2 (O):</span> <span id="player2Name">Waiting...</span>
            </div>
        </div>
        <div id="turnIndicator" class="text-center mb-4 text-lg font-medium">Waiting for players...</div>
        <div id="gameBoard" class="grid grid-cols-7 gap-1 bg-gray-800 p-4 rounded-lg shadow-lg"
            style="width: 560px; height: 560px;"></div>
    </div>

    <div id="modal">
        <div class="modal-content">
            <h2 id="modalTitle" class="font-fredoka text-3xl font-bold text-white mb-4"></h2>
            <p id="modalMessage" class="text-lg text-gray-200 mb-6"></p>
        </div>
    </div>

    <script>
        const progressBar = document.getElementById('progressBar');
        const loadingMessage = document.getElementById('loadingMessage');
        const loadingScreen = document.getElementById('loadingScreen');
        const gameUI = document.getElementById('gameUI');
        const gameBoard = document.getElementById('gameBoard');
        const player1Name = document.getElementById('player1Name');
        const player2Name = document.getElementById('player2Name');
        const turnIndicator = document.getElementById('turnIndicator');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');

        const messages = [
            "Warming up the engines...",
            "Connecting to the game servers...",
            "Looking for a game room...",
            "Loading awesome assets...",
            "Waiting for players...",
            "Assembling pixels...",
            "Almost there, get ready!",
            "Game ready!"
        ];

        let progress = 0;
        let messageIndex = 0;
        let ws;
        let mySymbol = null;
        let currentTurn = null;
        let players = {};
        let gameStarted = false;
        let gameOver = false;
        let gameBoard7x7 = Array(7).fill().map(() => Array(7).fill(''));
        let loadingIntervals = {};

        window.onload = function () {
            const cleanUrl = window.location.origin + '/game-play/';
            window.history.replaceState({}, document.title, cleanUrl);
            initializeGameBoard();
            fetchWebSocketUrl();
            simulateLoading();
        };

        function simulateLoading() {
            const messageInterval = setInterval(() => {
                if (messageIndex < messages.length - 1) {
                    messageIndex++;
                    loadingMessage.innerText = messages[messageIndex];
                }
            }, 1200);

            const progressInterval = setInterval(() => {
                if (progress < 80) {
                    progress += Math.random() * 2.5;
                    progressBar.style.width = Math.min(progress, 80) + '%';
                }
            }, 100);

            loadingIntervals = {messageInterval, progressInterval};
        }

        async function fetchWebSocketUrl() {
            try {
                const response = await fetch('/game-play/room-service', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                if (data.wsURL) {
                    connectWebSocket(data.wsURL);
                } else {
                    throw new Error('No WebSocket URL provided');
                }
            } catch (error) {
                console.error('Error fetching WebSocket URL:', error);
                loadingMessage.innerText = 'Error connecting to server...';
            }
        }

        function connectWebSocket(wsUrl) {
            ws = new WebSocket(wsUrl);

            ws.onopen = () => {
                console.log('WebSocket connected');
                loadingMessage.innerText = 'Connected! Waiting for players...';
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                console.log('Received message:', data);
                handleWebSocketMessage(data);
            };

            ws.onclose = () => {
                console.log('WebSocket closed');
                showErrorModal('Connection lost. Please try again.');
            };

            ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                showErrorModal('Connection error. Please try again.');
            };
        }

        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'game_state':
                    handleInitialGameState(data.state);
                    break;
                case 'game_start':
                    handleGameStart(data);
                    break;
                case 'game_update':
                    handleGameUpdate(data.state);
                    break;
                case 'personal_state':
                    handlePersonalState(data.state);
                    break;
                case 'player_joined':
                    handlePlayerJoined(data);
                    break;
                case 'player_left':
                    handlePlayerLeft(data);
                    break;
                case 'game_over':
                    handleGameOver(data);
                    break;
                case 'pong':
                    break;
                case 'error':
                    showErrorModal(data.message);
                    break;
                default:
                    console.warn('Unknown message type:', data.type);
            }
        }

        function handleGameStart(data) {
            console.log('Game starting:', data);
            gameStarted = true;
            gameOver = false;

            if (data.state) {
                handleGameUpdate(data.state);
            }

            if (loadingScreen.style.display !== 'none') {
                finishLoading();
            }
        }

        function handleInitialGameState(state) {
            console.log('Initial game state:', state);

            if (state.currentTurn) {
                currentTurn = state.currentTurn.toUpperCase();
            }
            if (state.players) {
                players = state.players;
            }
            if (state.isGameOver !== undefined) {
                gameOver = state.isGameOver;
            }
            if (state.board) {
                gameBoard7x7 = state.board;
            }

            console.log('Updated state - mySymbol:', mySymbol, 'currentTurn:', currentTurn, 'players:', players);

            updateBoard(gameBoard7x7);
            updatePlayerNames();
            updateTurnIndicator();
        }

        function handleGameUpdate(state) {
            console.log('Game update:', state);

            if (state.currentTurn) {
                currentTurn = state.currentTurn.toUpperCase();
            }
            if (state.players) {
                players = state.players;
            }
            if (state.isGameOver !== undefined) {
                gameOver = state.isGameOver;
            }
            if (state.board) {
                gameBoard7x7 = state.board;
                updateBoard(gameBoard7x7);
            }

            updatePlayerNames();
            updateTurnIndicator();
        }

        function handlePersonalState(state) {
            if (state.symbol) {
                mySymbol = state.symbol.toUpperCase();
            }
            console.log('Personal state:', state);
            handleGameUpdate(state);
        }

        function handlePlayerJoined(data) {
            console.log('Player joined:', data.username);
            if (data.state) {
                handleGameUpdate(data.state);
            }
        }

        function handlePlayerLeft(data) {
            console.log('Player left:', data.username);
            if (data.state) {
                handleGameUpdate(data.state);
            }
        }

        function handleGameOver(data) {
            console.log('Game over:', data);
            gameOver = true;
            handleGameUpdate(data.state);
            showGameEndModal(data);
        }

        function updatePlayerNames() {
            const player1Text = players['X'] || 'Waiting...';
            const player2Text = players['O'] || 'Waiting...';

            player1Name.innerText = player1Text;
            player2Name.innerText = player2Text;

            console.log('Updated player names - X:', player1Text, 'O:', player2Text);
        }

        function updateTurnIndicator() {
            if (gameOver) {
                turnIndicator.innerText = 'Game Over';
                turnIndicator.className = 'text-center mb-4 text-lg font-medium text-gray-400';
                return;
            }

            if (!currentTurn || !mySymbol) {
                turnIndicator.innerText = 'Waiting for game to start...';
                turnIndicator.className = 'text-center mb-4 text-lg font-medium text-gray-400';
                return;
            }

            const isMyTurn = currentTurn === mySymbol;
            const currentPlayerName = players[currentTurn] || 'Unknown';

            turnIndicator.innerText = isMyTurn
                ? 'Your turn!'
                : `Waiting for ${currentPlayerName}'s turn...`;
            turnIndicator.className = `text-center mb-4 text-lg font-medium ${isMyTurn ? 'text-blue-400' : 'text-orange-400'}`;

            console.log('Turn indicator updated - isMyTurn:', isMyTurn, 'currentTurn:', currentTurn, 'mySymbol:', mySymbol);
        }

        function finishLoading() {
            progress = 100;
            progressBar.style.width = '100%';
            loadingMessage.innerText = messages[messages.length - 1];

            if (loadingIntervals.messageInterval) {
                clearInterval(loadingIntervals.messageInterval);
            }
            if (loadingIntervals.progressInterval) {
                clearInterval(loadingIntervals.progressInterval);
            }

            setTimeout(() => {
                loadingScreen.classList.add('fade-out');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    document.body.style.overflow = 'auto';
                    gameUI.classList.add('active');
                }, 500);
            }, 1000);
        }

        function initializeGameBoard() {
            gameBoard.innerHTML = '';
            for (let i = 0; i < 7; i++) {
                for (let j = 0; j < 7; j++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell bg-gray-700 rounded-md';
                    cell.dataset.row = i;
                    cell.dataset.col = j;
                    cell.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleCellClick(i, j);
                    });
                    gameBoard.appendChild(cell);
                }
            }
            console.log('Game board initialized with', gameBoard.querySelectorAll('.cell').length, 'cells');
        }

        function handleCellClick(row, col) {
            console.log(`Cell clicked: [${row}, ${col}]`);
            console.log('Current state - gameOver:', gameOver, 'currentTurn:', currentTurn, 'mySymbol:', mySymbol);

            if (gameOver) {
                console.log('Click ignored: Game is over');
                return;
            }

            if (!mySymbol || !currentTurn) {
                console.log('Click ignored: Game not ready');
                return;
            }

            if (currentTurn !== mySymbol) {
                console.log(`Click ignored: Not your turn. Current: ${currentTurn}, Yours: ${mySymbol}`);
                return;
            }

            if (gameBoard7x7[row] && gameBoard7x7[row][col] && gameBoard7x7[row][col] !== '') {
                console.log('Click ignored: Cell already occupied with', gameBoard7x7[row][col]);
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                const action = {
                    type: 'game_action',
                    action: {
                        row: row,
                        col: col
                    }
                };
                console.log('Sending action:', action);
                ws.send(JSON.stringify(action));
            } else {
                console.error('WebSocket not ready:', ws ? ws.readyState : 'null');
            }
        }

        function updateBoard(board) {
            if (!board || !Array.isArray(board)) {
                console.warn('Invalid board data:', board);
                return;
            }

            console.log('Updating board:', board);

            for (let i = 0; i < 7; i++) {
                for (let j = 0; j < 7; j++) {
                    const cell = gameBoard.querySelector(`[data-row="${i}"][data-col="${j}"]`);
                    if (cell) {
                        const symbol = board[i] && board[i][j] ? board[i][j] : '';
                        const displaySymbol = symbol.toUpperCase();

                        cell.innerText = displaySymbol;
                        cell.className = `cell bg-gray-700 rounded-md ${displaySymbol ? displaySymbol.toLowerCase() : ''}`;

                        if (displaySymbol) {
                            cell.classList.add('occupied');
                        } else {
                            cell.classList.remove('occupied');
                        }
                    }
                }
            }
        }

        function showGameEndModal(data) {
            const winner = data.winner;
            const winnerName = winner ? players[winner] : null;
            const isPrematureEnd = !winner;

            if (isPrematureEnd) {
                modalTitle.innerText = 'Game Ended';
                modalMessage.innerText = 'A player left the game early. The game has ended with no winner.';
            } else {
                modalTitle.innerText = winner ? `${winnerName} Wins!` : 'Draw!';
                modalMessage.innerText = winner
                    ? `🎉 Congratulations to ${winnerName} for winning the game!`
                    : 'The game ended in a draw. Want to play again?';
            }

            modal.classList.add('show');

            if (winner) {
                confetti({
                    particleCount: 120,
                    spread: 90,
                    origin: {y: 0.6}
                });
            }

            redirect();

        }

        function showErrorModal(message) {
            modalTitle.innerText = 'Game Error';
            modalMessage.innerText = message;
            modal.classList.add('show');

            redirect();
        }

        function redirect() {

            fetch('/game-play/get-return-url', {credentials: 'include'})
                .then(res => res.json())
                .then(({returnUrl}) => {
                    let countdown = 5;
                    modalMessage.innerHTML += `<br><br><span id="countdownTimer" class="text-sm text-amber-300 font-medium">Redirecting in ${countdown} seconds...</span>`;

                    const timer = setInterval(() => {
                        countdown--;
                        const timerEl = document.getElementById('countdownTimer');
                        if (timerEl) {
                            if (countdown <= 0) {
                                clearInterval(timer);
                                window.location.href = returnUrl;
                            } else {
                                timerEl.innerText = `Redirecting in ${countdown} seconds...`;
                            }
                        }
                    }, 1000);
                })
                .catch(err => {
                    console.error('Failed to fetch return URL:', err);
                });
        }

        setInterval(() => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({type: 'ping'}));
            }
        }, 30000);

    </script>
</body>

</html>
