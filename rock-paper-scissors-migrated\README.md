# Rock Paper Scissors - Migrated Version

This is the migrated version of the Rock Paper Scissors game, refactored to work with the existing server infrastructure following the same architecture as the Tic Tac Toe game.

## Migration Changes

### Architecture Changes
- **Removed Socket.IO dependency**: Replaced with native WebSocket communication
- **Removed bot mode**: Game now only supports player vs. player matches
- **Integrated loading screen**: Added the same loader/spinner from Tic Tac Toe for consistency
- **Backend-agnostic design**: Game now works with the existing GameAPI interface

### File Structure
```
rock-paper-scissors-migrated/
├── index.html          # Main game interface
├── script.js           # Game logic and WebSocket communication
├── style.css           # Retro 90s styling (preserved from original)
├── server.go           # Go server implementation using GameAPI
└── README.md           # This file
```

### Key Features Preserved
- **Retro 90s aesthetic**: Maintained the original visual design and styling
- **3-round gameplay**: Best-of-three logic with score tracking
- **Real-time multiplayer**: Player vs. player matches
- **Responsive design**: Works on desktop and mobile devices
- **Visual feedback**: Choice highlighting, results display, and confetti effects

### Key Features Removed
- **Bot mode**: No longer supports single-player against AI
- **Socket.IO**: Replaced with native WebSocket
- **Room creation/joining UI**: Now handled by the server infrastructure
- **Custom server configuration**: Uses the existing server setup

### Technical Implementation

#### Frontend (JavaScript)
- Uses native WebSocket for real-time communication
- Follows the same message protocol as Tic Tac Toe
- Handles game states: waiting, playing, results, game over
- Implements proper error handling and connection management

#### Backend (Go)
- Implements the `gameapi.Game` interface
- Supports 2-player Rock Paper Scissors with configurable rounds
- Handles player actions, game state management, and round results
- Uses best-of-N logic for determining winners

#### Game Flow
1. **Loading**: Shows spinner while connecting to server
2. **Waiting**: Displays when waiting for second player
3. **Playing**: Players make choices (rock, paper, scissors)
4. **Results**: Shows round results and updated scores
5. **Game Over**: Displays final winner with confetti effect

### Integration Notes
- The game follows the same WebSocket message protocol as Tic Tac Toe
- Server implementation uses the existing GameAPI interface
- Frontend fetches WebSocket URL from `/game-play/room-service`
- Maintains session management through cookies

### Deployment
This migrated version is designed to be deployed alongside the existing Tic Tac Toe game on the same server infrastructure with minimal configuration changes.
