<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GamyDay Games - Rock Paper Scissors</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <!-- Loading Screen -->
    <div id="loadingScreen">
        <div class="loading-content">
            <h1 class="loading-title font-retro">Rock Paper Scissors</h1>
            <div class="loading-spinner"></div>
            <div class="progress-container">
                <div id="progressBar" class="progress-bar" style="width: 0%"></div>
            </div>
            <div id="loadingMessage" class="loading-message">Connecting to server...</div>
        </div>
    </div>

    <!-- Game UI -->
    <div id="gameUI" class="flex flex-col items-center justify-center min-h-screen">
        <div class="game-container">
            <h1 class="game-title font-retro">Rock Paper Scissors</h1>

            <!-- Players Info -->
            <div class="players-info">
                <div class="player-info">
                    <span class="text-blue-400">Player 1:</span>
                    <span id="player1Name" class="player-name">Waiting...</span>
                </div>
                <div class="player-info">
                    <span class="text-orange-400">Player 2:</span>
                    <span id="player2Name" class="player-name">Waiting...</span>
                </div>
            </div>

            <!-- Game Status -->
            <div id="gameStatus" class="game-status">
                <div id="roundInfo">Round 1 of 3</div>
                <div id="scoreInfo">Score: 0 - 0</div>
                <div id="turnInfo">Waiting for players...</div>
            </div>

            <!-- Choice Buttons -->
            <div class="choices-container">
                <button class="choice-btn" data-choice="rock">
                    <span class="choice-emoji">✊</span>
                    <span class="choice-name">Rock</span>
                </button>
                <button class="choice-btn" data-choice="paper">
                    <span class="choice-emoji">✋</span>
                    <span class="choice-name">Paper</span>
                </button>
                <button class="choice-btn" data-choice="scissors">
                    <span class="choice-emoji">✌️</span>
                    <span class="choice-name">Scissors</span>
                </button>
            </div>

            <!-- Results Display -->
            <div id="resultsContainer" class="results-container">
                <div id="roundResult" class="round-result"></div>
                <div class="choices-display">
                    <div class="player-choice">
                        <span id="player1Choice" class="choice-display-emoji"></span>
                        <div id="player1ChoiceName"></div>
                    </div>
                    <div class="player-choice">
                        <span id="player2Choice" class="choice-display-emoji"></span>
                        <div id="player2ChoiceName"></div>
                    </div>
                </div>
                <div id="gameOverMessage" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <h2 id="modalTitle" class="modal-title"></h2>
            <p id="modalMessage" class="modal-message"></p>
            <button id="modalButton" class="modal-button">OK</button>
        </div>
    </div>

    <!-- Credits Footer -->
    <footer class="fixed bottom-4 left-4 text-xs opacity-60">
        <div class="text-cyan-400">Created by Shushie</div>
    </footer>

    <script src="script.js"></script>
</body>

</html>
