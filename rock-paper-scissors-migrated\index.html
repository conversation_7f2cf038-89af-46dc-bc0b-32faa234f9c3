<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GamyDay Games - Rock Paper Scissors</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Orbitron:wght@400;700;900&family=Fredoka:wght@400;600;700&family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <!-- Loading Screen - Exact copy from Tic <PERSON> -->
    <div id="loadingScreen"
        class="fade-in-up flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-[#4c1d95] via-[#6b21a8] to-[#1e1b4b] p-6 text-white">
        <div class="mb-8 logo-float">
            <svg width="120" height="120" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="logoGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#38bdf8" />
                        <stop offset="100%" style="stop-color:#6366f1" />
                    </linearGradient>
                    <linearGradient id="logoGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#fbbf24" />
                        <stop offset="100%" style="stop-color:#f97316" />
                    </linearGradient>
                    <linearGradient id="logoGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ec4899" />
                        <stop offset="100%" style="stop-color:#d946ef" />
                    </linearGradient>
                </defs>
                <g transform="rotate(15 100 100)">
                    <path
                        d="M100 20 C144.18 20 180 55.82 180 100 C180 144.18 144.18 180 100 180 C55.82 180 20 144.18 20 100"
                        fill="none" stroke="url(#logoGradient1)" stroke-width="20" stroke-linecap="round" />
                    <path d="M100 20 C55.82 20 20 55.82 20 100 C20 144.18 55.82 180 100 180" fill="none"
                        stroke="url(#logoGradient2)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(120 100 100)" />
                    <path d="M20 100 C20 55.82 55.82 20 100 20 C144.18 20 180 55.82 180 100" fill="none"
                        stroke="url(#logoGradient3)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(240 100 100)" />
                    <circle cx="100" cy="100" r="25" fill="white" />
                </g>
            </svg>
        </div>
        <h1 class="font-fredoka text-4xl md:text-5xl font-bold text-center text-white tracking-wider text-glow mb-6">
            GamyDay Games
        </h1>
        <div class="w-full max-w-md mt-4 mb-8">
            <div class="h-4 bg-white/10 rounded-full overflow-hidden shadow-inner relative">
                <div id="progressBar"
                    class="h-full bg-gradient-to-r from-amber-400 to-orange-500 rounded-full transition-all duration-500 ease-out relative progress-bar-shine glow-bar"
                    style="width: 0%;"></div>
            </div>
            <p id="loadingMessage" class="text-center mt-4 text-sm text-gray-300 h-5 font-medium tracking-wide">
                Initializing...
            </p>
        </div>
        <div class="absolute bottom-6 text-center text-sm text-gray-400">
            powered by <span class="font-semibold text-white">Amrita</span>
        </div>
    </div>

    <!-- Game UI -->
    <div id="gameUI" class="flex flex-col items-center justify-center min-h-screen">
        <div class="game-container">
            <h1 class="game-title font-retro">Rock Paper Scissors</h1>

            <!-- Players Info -->
            <div class="players-info">
                <div class="player-info">
                    <span class="text-blue-400">Player 1:</span>
                    <span id="player1Name" class="player-name">Waiting...</span>
                </div>
                <div class="player-info">
                    <span class="text-orange-400">Player 2:</span>
                    <span id="player2Name" class="player-name">Waiting...</span>
                </div>
            </div>

            <!-- Game Status -->
            <div id="gameStatus" class="game-status">
                <div id="roundInfo">Round 1 of 3</div>
                <div id="scoreInfo">Score: 0 - 0</div>
                <div id="turnInfo">Waiting for players...</div>
            </div>

            <!-- Choice Buttons -->
            <div class="choices-container">
                <button class="choice-btn" data-choice="rock">
                    <span class="choice-emoji">✊</span>
                    <span class="choice-name">Rock</span>
                </button>
                <button class="choice-btn" data-choice="paper">
                    <span class="choice-emoji">✋</span>
                    <span class="choice-name">Paper</span>
                </button>
                <button class="choice-btn" data-choice="scissors">
                    <span class="choice-emoji">✌️</span>
                    <span class="choice-name">Scissors</span>
                </button>
            </div>

            <!-- Results Display -->
            <div id="resultsContainer" class="results-container">
                <div id="roundResult" class="round-result"></div>
                <div class="choices-display">
                    <div class="player-choice">
                        <span id="player1Choice" class="choice-display-emoji"></span>
                        <div id="player1ChoiceName"></div>
                    </div>
                    <div class="player-choice">
                        <span id="player2Choice" class="choice-display-emoji"></span>
                        <div id="player2ChoiceName"></div>
                    </div>
                </div>
                <div id="gameOverMessage" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <h2 id="modalTitle" class="modal-title"></h2>
            <p id="modalMessage" class="modal-message"></p>
            <button id="modalButton" class="modal-button">OK</button>
        </div>
    </div>

    <!-- Credits Footer -->
    <footer class="fixed bottom-4 left-4 text-xs opacity-60">
        <div class="text-cyan-400">Created by Shushie</div>
    </footer>

    <script src="script.js"></script>
</body>

</html>
